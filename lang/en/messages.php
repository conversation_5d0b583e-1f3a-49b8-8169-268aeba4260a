<?php

return [

    // Common Words
    'latitude' => 'Latitude',
    'longitude' => 'Longitude',
    'last_update' => 'Last Update',
    'device_imei' => 'Device IMEI',
    'dashboard' => 'Dashboard',
    'device' => 'Device',
    'dealer' => 'Dealer',
    'client' => 'Client',
    'type' => 'Type',
    'test' => 'Test',
    'name' => 'Name',
    'username' => 'Username',
    'password' => 'Password',
    'company' => 'Company',
    'address' => 'Address',
    'tax_code' => 'Tax Code',
    'vat_number' => 'VAT Number',
    'municipality' => 'Municipality',
    'zip_code' => 'Zip Code',
    'province' => 'Province',

    // Table Common Words
    'export' => 'Export',
    'add_new_record' => 'Add New Record',
    'add' => 'Add',
    'edit' => 'Edit',
    'submit' => 'Submit',
    'cancel' => 'Cancel',
    'details' => 'Details',
    'delete' => 'Delete',
    'close' => 'Close',
    'history' => 'History',
    'action' => 'Action',
    'confirm_deletion' => 'Confirm Deletion',
    'delete_message' => 'Are you sure you want to delete this',

    // Login
    'login_title' => 'Welcome to AllTech Web! 👋',
    'login_subtitle' => 'Please sign-in to your account to access data!',
    'login_email_label' => 'Email or Username',
    'login_email_placeholder' => 'Enter your email or username',
    'login_password_label' => 'Password',
    'login_signin_button' => 'Sign in',
    'login_user_empty' => 'Please enter username or email.',
    'login_pass_empty' => 'Please enter your password.',

    // Menu
    'menu_welcome_message' => 'Hello There',
    'menu_dashboard' => 'Dashboard',
    'devices' => 'Devices',
    'dealers' => 'Dealers',
    'clients' => 'Clients',
    'contracts' => 'Contracts',
    'admin' => 'Administrators',
    'settings' => 'Settings',
    'logout' => 'Logout',
    'verified' => 'Verified',

    'on' => 'On',
    'off' => 'Off',

    // Dashboard
    'dash_live_mode' => 'LIVE MODE',
    'dash_history_mode' => 'HISTORY MODE',

    // GEO Fence
    'geofence_cancel_drawing_title' => 'Cancel drawing',
    'geofence_cancel_drawing_text' => 'Cancel',
    'geofence_finish_drawing_title' => 'Finish drawing',
    'geofence_finish_drawing_text' => 'Finish',
    'geofence_delete_last_point_title' => 'Delete last point drawn',
    'geofence_delete_last_point_text' => 'Delete last point',
    'geofence_draw_polyline' => 'Draw a polyline',
    'geofence_draw_polygon' => 'Draw a polygon',
    'geofence_draw_rectangle' => 'Draw a rectangle',
    'geofence_draw_circle' => 'Draw a circle',
    'geofence_draw_marker' => 'Draw a marker',
    'geofence_draw_circlemarker' => 'Draw a circlemarker',
    'geofence_circle_tooltip_start' => 'Click and drag to draw circle.',
    'geofence_radius' => 'Radius',
    'geofence_circlemarker_tooltip_start' => 'Click map to place circle marker.',
    'geofence_marker_tooltip_start' => 'Click map to place marker.',
    'geofence_polygon_tooltip_start' => 'Click to start drawing shape.',
    'geofence_polygon_tooltip_cont' => 'Click to continue drawing shape.',
    'geofence_polygon_tooltip_end' => 'Click first point to close this shape.',
    'geofence_polyline_error' => '<strong>Error:</strong> shape edges cannot cross!',
    'geofence_polyline_tooltip_start' => 'Click to start drawing line.',
    'geofence_polyline_tooltip_cont' => 'Click to continue drawing line.',
    'geofence_polyline_tooltip_end' => 'Click last point to finish line.',
    'geofence_rectangle_tooltip_start' => 'Click and drag to draw rectangle.',
    'geofence_simpleshape_tooltip_end' => 'Release mouse to finish drawing.',
    'geofence_save_changes_title' => 'Save changes',
    'geofence_save_changes_text' => 'Save',
    'geofence_cancel_editing_title' => 'Cancel editing, discards all changes',
    'geofence_cancel_editing_text' => 'Cancel',
    'geofence_clear_all_layers_title' => 'Clear all layers',
    'geofence_clear_all_layers_text' => 'Clear All',
    'geofence_edit_layers' => 'Edit layers',
    'geofence_edit_disabled' => 'No layers to edit',
    'geofence_remove_layers' => 'Delete layers',
    'geofence_remove_disabled' => 'No layers to delete',
    'geofence_edit_tooltip_text' => 'Drag handles or markers to edit features.',
    'geofence_edit_tooltip_subtext' => 'Click cancel to undo changes.',
    'geofence_remove_tooltip_text' => 'Click on a feature to remove.',

    // Contracts
    'number_plate' => 'Number Plate',
    'brand' => 'Brand',
    'model' => 'Model',
    'signed' => 'Signed',
    'unsigned' => 'Unsigned',
    'duration' => 'Duration',
    'months' => 'Months',
    'start_date' => 'Starting Date',
    'end_date' => 'End Date',
    'notifications' => 'Notifications',
    'today' => 'Today',
    'all' => 'All',
    'yesterday' => 'Yesterday',
    'role' => 'Role',

    'stopped' => 'Stopped',
    'active' => 'Active',
    'moving' => 'Moving',
    'play' => 'Play',
    'pause' => 'Pause',
    'logout_confirmation' => 'Are You Sure You Want to Log Out?',
    'logout_warning' => 'Logging out will end your current session. You can log back in anytime to continue managing your vehicles and devices.',
    'cancel' => 'Cancel',
    'logout' => 'Logout',

    'sleep' => 'Sleep',
    'deep_sleep' => 'Deep Sleep',
    'online_sleep' => 'Online Sleep',
    'ultra_sleep' => 'Ultra Sleep',

    'vehicle_type' => 'Vehicle Type',
    'select_vehicle_type' => 'Select Vehicle Type',
    'number' => 'Number',
    'device_name' => 'Device name',
    'sign' => 'Sign',
    'search' => 'Search',

    'welcome_back' => 'Welcome Back to MeMove',
    'manage_monitor' => 'Manage and monitor your devices efficiently and securely.',
    'track_real_time' => 'Monitor real-time data, manage alarms, and configure settings with ease.',
    'keep_logged_in' => 'Keep me logged in',
    'efficient_management' => 'Efficient device management made simple.',
    'sign_in' => 'Sign In',
    'reset_password_instructions' => 'Enter your email to receive instructions to reset your password.',
    'reset_password' => 'Reset Password',


    'add_client' => 'Add Client',
    'company_name' => "Company Name",
    'add_dealer' => "Add Dealer",

    'add_device' => 'Add Device',
    'edit_device' => 'Edit Device',
    'add_contract' => 'Add Contract',
    'edit_contract' => 'Edit Contract',
    'status' => 'Status',
    'add_admin' => 'Add Admin',

    'contract_heading' => 'Contract Response',
    'contract_paragraph' => 'Please review and choose to accept or reject this contract.',
    'accept' => 'Accept',
    'reject' => 'Reject',

    'contract_accepted' => 'You have successfully accepted the contract.',
    'contract_rejected' => 'You have rejected the contract.',

    'password_confirmation' => 'Password Confirmation',
    'admins' => 'Admins',
    'inactive' => 'Inactive',
    'added_at' => 'Added At',
    'actions' => 'Actions',

    'send_password_reset_link' => 'Send Password Reset Link',
    'sending' => 'Sending',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'no_record_found' => 'No record found',
    'select_a_role' => 'Select a Role',
    'admin' => 'Admin',
    'technician' => 'Technician',
    'warehouse_operator' => 'Warehouse Operator',
    'delete_confirmation' => 'Are you sure you want to delete this record?',
    'delete_warning' => 'Once deleted, this action cannot be undone.',

    'incharge_of_transport' => 'IN CHARGE OF TRANSPORT',
    'transport_reason' => 'REASON FOR TRANSPORT',
    'delivery_terms' => 'DELIVERY TERMS',
    'parcels' => 'PARCELS',
    'weight' => 'WEIGHT',

    'optional' => 'Optional',

    'transport_officer_signature' => 'Transport Officer Signature',
    'recipient_signature' => 'Recipient Signature',
    'working' => 'Working',
    'working_heading' => 'Working',

    'not_working' => 'Not Working',
    'download' => 'Download',
    'return_ddt' => 'Return Delivery Note (DDT)',
    'return_reason' => 'Return Reason',
    'operations_center' => 'Operations Center',
    'ddt_type' => 'DDT Type',
    'command' => 'Command',
    'custom_command' => 'Custom command',
    'enter_custom_command' => 'Enter Custom command',
    'send_command' => 'Send Command',
    'quadro' => 'Quadro',

    'block_motor' => 'Starting Block',
    'export_png' => 'Esporta come PNG',
    'nautical_miles' => 'Nautical Miles',

    'gps_off' => 'GPS Off',
    'gps_signal_good' => 'GPS Signal: Good',
    'gps_signal_weak' => 'GPS Signal: Weak',
    'gps_inactive' => 'GPS Inactive',
    'low_power_mode' => 'Low Power Mode',
    'low_power_online' => 'Low Power - Online',
    'battery_saver_mode' => 'Battery Saver Mode',

    'good' => 'Good',
    'weak' => 'Weak',
    'inactive' => 'Inactive',

    'created_at' => 'Created At',
    'records_selected' => 'Record Selected',
    'device_history' => 'Device History',
    'test_device' => 'Test Device',
    'test_device_results' => 'Device Test Results',


    'truck' => 'Truck',
    'car' => 'Car',
    'motorcycle' => 'Motorcycle',
    'scooter' => 'Scooter',
    'bus' => 'Bus',
    'van' => 'Van',
    'tractor' => 'Tractor',
    'boat' => 'Boat',
    'assign_devices' => 'Assign Devices',
    'no_associated_client' => 'No Associated Client',
    'device_details' => 'Device Detials',
    'device_test_instructions' => 'Ensure the device is connected and click \'Test Device\' to verify if the GPS and other data are being sent correctly.',

    'no_sleep' => 'No Sleep',
    'gps_sleep' => 'GPS Sleep',
    'deep_sleep' => 'Deep Sleep',
    'online_sleep' => 'Online Sleep',
    'ultra_sleep' => 'Ultra Sleep',
    'unknown' => 'Unknown',



    'test_device_result' => 'Test Device Result',


    'gps_tested' => 'GPS and device data are being transmitted correctly.',
    'device_data_not_found' => 'Device data not found.',
    'movement' => 'Movement',
    'ignition' => 'Ignition',
    'location' => 'Location',
    'speed' => 'Speed',
    'test_motor_block' => 'Test Starter Block',
    'load_result' => 'Load Result',
    'loading' => 'Loading',
    'response' => 'Response',
    'response_received_at' => 'Response Received At',
    'tested_at' => 'Tested At',
    'color' => 'Color',
    'frame' => 'Frame',
    'registration_number' => 'Registration Number',
    'chassis' => 'Chassis',
    'registration_date' => 'Registration Date',
    'travelled_distance' => 'Travelled Distance',
    'starter_motor_block' => 'Starter Block',
    'yes' => 'Yes',
    'no' => 'No',
    'select_duration' => 'Select Duration',
    'months' => 'Months',
    'show_example' => 'Show Example',


    'mark_device_purchased' => 'Mark Services Purchased',
    'import_devices' => 'Import Devices',
    'import_instructions' => 'Note: Please upload the Excel file in the format shown in the example below.',
    'import_instructions_details' => 'Info: You can select the desired columns in the FOTA web interface and upload the file directly without changes.',


    'no_comamnd_response' => 'No command response received yet',
    'retry_test' => 'Retry Test (if there is an error)',
    'finish_test' => 'Finish Test',
    'motor_block_test' => 'Ignition Test',
    'ignition_tested' => 'Ignition was successfully tested at',
    'no_test_result' => 'No test result found for this device.',
    'last_test_result' => 'Device Last Test Result',
    'phone_number' => 'Phone Number',

    'perform_testing' => 'Perform the Test',


    'export_pdf' => 'Export PDF',
    'export_excel' => 'Export Excel',
    'passkey' => 'Passkey',
    'completed' => 'Completed',
    'queue' => 'Queue',
    'received_at' => 'Received at',
    'sent_at' => 'Sent at',
    'delete_from_queue' => 'Delete From Queue',
    'cancelled' => 'Cancelled',
    'contract_number' => 'Contract Number',
    'assigned' => 'Assigned',
    'returned' => 'Returned',
    'devices_alarms_events' => 'Devices Alarms/Event',
    'operator' => 'Operator',
    'block_motor_command_sent' => 'Starting Block command has been sent!',
    'unblock_motor_command_sent' => 'Starting UnBlock command has been sent!',

    'pending' => 'Pending',
    'expired' => 'Expired',
    'rejected' => 'Rejected',
    'view' => 'View',
    'downlod_collaudo_contract' => 'Downlod Collaudo Contract',
    'downlod_contract' => 'Downlod Contract',
    'device_commands' => 'Devices Commands',
    'send_contract' => 'Send Contract To Client',
    'select_an_option' => 'Select an option',
    'contract_details' => 'Contract Details',
    'vehicle_brand' => 'Vehicle Brand',
    'client_response_received_at' => 'Client Response Received At',
    'data_not_found' => 'Data Not Found',

    'testing' => 'Testing',
    'tested' => 'Tested',
    'not_tested' => 'Not Tested',
    'assign_a_dealer' => 'Assign A Dealer',
    'return_devices_request' => 'Return Devices Request',
    'select' => 'Select',
    'not_verified' => 'Not Verified',
    'event' => 'Event',
    'occured_at' => 'Occured At',
    'show_address' => 'Show Address',
    'no_addres_found' => 'No Addres Found',
    'start_time' => 'Start Time',
    'end_time' => 'End Time',
    'load_history' => 'Load History',
    'coordinates' => 'Coordinates',
    'gps_date' => 'Gps Date',
    'odometer' => 'Odometer',
    'timestamp' => 'Timestamp',
    'history_time' => 'History Time',
    'exporting' => 'Exporting',
    'street_name' => 'Street Name',
    'stop_alaram' => 'Stop Alaram',
    'alaram_sound_enabled' => 'Alaram Sound Enabled',
    'log' => 'Log',
    'device_log' => 'Device Log',
    'select_action' => 'Select Action',
    'updated_at' => 'Updated At',
    'vehicle_verification_type' => 'Vehicle Verification Type',
    'client_information' => 'Client Information',
    'language' => 'Language',



    'contact_owner' => 'Contacted owner',
    'contact_the_force_of_order' => 'Contact the police',
    'false_alaram' => 'False Alaram',
    'other' => 'Other',
    'notes' => 'Notes',
    'enter_notes' => 'Enter additional notes',
    'device_user_details' => 'Device and User Details',
    'add_notes' => 'Add notes in device log',
    'device_history_report' => 'Device History Report',
    'exported_date' => 'Exported Date',
    'total_distance_covered' => 'Total Distance Covered',
    'italy_time' => 'Italy Time',
    'device_information' => 'Device Information',
    'additional_vehicle_info' => 'Additional Vehicle Information',
    'device_dealer_info' => 'Device Dealer Information',
    'device_geofence' => 'Device Geofence',

    'incorrect_username_or_email' => 'Username or email is incorrect',
    'user_not_found' => 'User not found',
    'account_deactivated' => 'Account is deactivated',
    'incorrect_password' => 'Password is incorrect',
    'expired_token' => 'Invalid or expired reset link.',
    'password_reset_success' => 'Password reset successfully',


    'devices_imported' => 'Devices imported successfully',
    'devices_import_error' => 'Error importing devices',
    'assign_not_tested_device_error' => 'You cannot assign a device to a dealer that is Not Tested',
    'device_updated' => 'Device updated successfully',
    'device_created' => 'Device created successfully',
    'client_created' => 'Client created successfully',
    'client_updated' => 'Client updated successfully',
    'dealer_created' => 'Dealer created successfully',
    'dealer_updated' => 'Dealer updated successfully',
    'device_deleted' => 'Device deleted successfully',
    'live_data_not_found' => 'Live data not found',
    'test_retried' => 'Test retried successfully',
    'no_live_data_from_device' => 'No live data found from this device',
    'device_tested' => 'The device has been successfully tested!',
    'no_test_result' => 'No test result found',
    'device_not_found' => 'Device not found',
    'device_inspection_completed' => 'Testing successfully completed',
    'device_not_tested_error' => 'Device is not tested! Please test this device to assign the dealer.',
    'device_not_working_error' => 'Device doesn’t have status "working"! Please change device status to working to assign the dealer.',
    'dealer_assigned' => 'Dealer assigned successfully',
    'command_already_in_queue' => 'This command is already in the queue for the specified IMEI.',
    'command_added_in_queue' => 'Command added to queue successfully!',
    'imei_not_received' => 'IMEI not received! Please try again.',
    'return_ddt_generated' => 'Return DDT generated and sent to the dealer successfully!',
    'dealer_not_found_for_device' => 'Dealer not found for some devices! Please unselect or assign a delaer to that devices!',
    'command_removed' => 'Command removed from queue successfully!',
    'cannot_delete_active_contract_client' => 'You cannot delete this client that has an active contract!',
    'client_deleted' => 'Client deleted successfully',
    'dealer_deleted' => 'Dealer deleted successfully!',
    'password_reset_link_sent' => 'Password reset link has been sent!',



    'delete_exiting_geofence' => 'Delete existing geofence to add new for device:',
    'geofence_saved_success' => 'Geofence saved successfully!',
    'no_notification_found' => 'No notification found',
    'view_all' => 'View All',



    'devices_selected' => 'Devices Selected',
    'show_on_map' => 'Show on map',
    'welcome' => 'Welcome',

    'voltage' => 'Voltage',
    'engine_status' => 'Engine Status',
    'ignition_status' => 'Ignition Status',
    'battery_voltage' => 'Vehicle Battery Voltage',
    'surname' => 'Surname',

    'surname_company_name' => 'Surname/Company Name',
    'private' => 'Private',
    'last_name' => 'Last Name',
    'upload_installation_photo' => 'Upload a clear photo showing where the device was mounted in the customer\'s vehicle.',
    'click_to_upload' => 'Click to upload',
    'installation_image' => 'Installation Image',

    'memove_privacy' => 'That your Personal Data will be processed by the Company, also with the aid of electronic and/or automated means, for purposes related to our activities such as: the marketing of products and services, the sending of informational/promotional advertising material and updates on initiatives and offers, market research, economic and statistical analyses.',

    'memove_partners_privacy' => 'That your Personal Data will be processed by our Partner Companies, also with the aid of electronic and/or automated means, for purposes related to our activities such as: the marketing of products and services, the sending of advertising/informational/promotional material and updates on initiatives and offers, market research, economic and statistical analyses.',

    'non_accept' => 'Does Not Accept',
    'signed_at' => 'Signed At',
    'platform_login' => 'Login to Platform',


    'account_not_exist' => 'Account does not exist!',
    'account_deactivated' => 'Your account has been deactivated.',
    'login_success' => 'Logged in successfully!',
    'incorrect_password' => 'Incorrect password!',
    'auth_failed' => 'Authentication failed.',
    'logout_success' => 'Logged out successfully!',
    'language_updated' => 'Language updated successfully!',
    'profile_updated' => 'Profile updated successfully!',
    'alert_settings_updated' => 'Alert settings updated successfully!',
    'geofence_not_found' => 'Geofence not found',
    'geofence_deleted' => 'Geofence deleted successfully',

    'admin_updated' => 'Admin updated successfully!',
    'admin_created' => 'Admin created successfully!',
    'admin_deleted' => 'Admin deleted successfully!',
    'no_account_found' => 'No account found with that email.',
    'password_reset_sent' => 'Password reset link has been sent!',

    'other' => 'Other',
    'setdigout_1' => 'Turn ON lock',
    'setdigout_0' => 'Turn OFF lock',
    'cpureset' => 'Reset device',
    'web_connect' => 'Force FOTA web connection',
    'getinfo' => 'Get system info',
    'getver' => 'Get code version, IMEI, and more',
    'getstatus' => 'Get modem status',
    'getgps' => 'Get current GPS data',
    'getio' => 'Read IO data',
    'ggps' => 'Get location with Google Maps link',
    'fwstats' => 'Get firmware stats',
    'getrecord' => 'Save and send high priority record',
    'getimeiccid' => 'Get IMEI and ICCID',
    'getimsi' => 'Get SIM IMSI number',
    'getops' => 'List visible operators',
    'allver' => 'Get hardware and firmware versions',
    'countrecs' => 'Count records',
    'deleterecords' => 'Delete all SD card records',
    'cleardigoutprio' => 'Clear DOUT priority',
    'setigndigout' => 'Set DOUT when ignition is off',
    'battery' => 'Get battery state info',
    'wdlog' => 'Get watchdog info',
    'defaultcfg' => 'Load default configuration',
    'sdformat' => 'Format SD card',


    'contract_not_found' => 'Contract not found.',
    'contract_status_message' => 'You already have :status this contract',
    'memove_privacy_error' => 'MeMove privacy should be accepted!',
    'memove_partner_privacy_error' => 'MeMove partners privacy should be accepted!',
    'accepted' => 'Accepted',

    'contract_updated' => 'Contract updated successfully!',
    'contract_created' => 'Contract created successfully!',
    'contract_deleted' => 'Contract deleted successfully!',
    'ddt_deleted' => 'DDT deleted successfully!',
    'ddt_file_not_found' => 'The requested DDT file does not exist.',

    'no_device_selected' => 'No device selected!',
    'device_log_updated' => 'Device log updated successfully!',
    'device_log_created' => 'Device log created successfully!',
    'device_log_deleted' => 'Device Log deleted successfully!',
    'client_not_found' => 'Client not found!',
    'renew' => 'Renew',
    'change_vehicle_ownership' => 'Change Vehicle/Ownership',
    'unauthorized_account_deactivated' => 'Unauthorized! Your account is deactivated!',
    'inactive_client_account' => 'Inactive client account',
    'geofence_reponse_submitted' => 'Geofence exit response submitted successfully!',
    'gps_signal' => 'GPS Signal',
    'battery_level' => 'Battery Level',
    'plate' => 'Plate',
    'open' => 'Open',
    'suspended' => 'Suspended',
    'under_management' => 'Under Management',
    'closed' => 'Closed',
    'show_archived' => 'Show Archived',
    'power_supply' => 'Device not installed correctly, if you ask to check the power',
    'automatic_geofence' => 'Automatic Geofence',
    'geofence_alarm' => 'Geofence alarm',
    'geofence' => 'Geofence',
    'hide_details' => 'Hide Details',
    'show_details' => 'Show Details',
    'activate_alarm' => 'Activate Alarm',
    'deactivate_alarm' => 'Deactivate Alarm',

    'installation_error' => 'Warning: Vehicle battery and DIN1 are both at 0. Check the installation.',
    'ignition_off' => 'Error: Ignition is off. Please check the wiring and power supply.',
    'notify_for_alarams' => 'Notify For Alarams',
    'date' => 'Date',

    'enter_km' => 'Enter current km',
    'in_km' => 'In Km',

    'perform_the_test' => 'Perform the Test',
    'contract_sms_sent' => 'Contract sent to the client via SMS successfully!',
    'send_contract_via_sms' => 'Send Contract via SMS',
    'in_maintenance' => 'In Maintenance',
    'maintenance' => 'Maintenance',
    'ain1_enabled' => 'AIN1 Enabled',
];
