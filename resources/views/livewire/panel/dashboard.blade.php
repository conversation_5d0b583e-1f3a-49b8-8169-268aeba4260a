@section('dashboard_active', 'bg-primary/10 text-primary')
@php
    $user = auth()->user();
@endphp
@push('styles')
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.css" />
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/leaflet-easyprint@2.1.9/dist/bundle.min.js"></script>
    <link rel="stylesheet" href="{{ asset('assets/css/map.css') }}">
@endpush

<main wire:ignore class="w-full ps-5">

    @if ($user->role == 'dealer')
        <div
            class="absolute z-40 px-4 py-2 mx-auto text-sm font-semibold bg-white rounded-full shadow-md text-primary top-5 right-5 w-fit">
            @lang('messages.welcome'), {{ $user->name }}
        </div>
    @endif


    <!-- map -->
    <section wire:ignore>
        <div class="h-[90vh] w-full relative -z-0">
            <div id="worldMap" class="w-full h-full"></div>
        </div>
    </section>
    <div id="player">
        <div class="flex items-center justify-between w-full gap-3">
            <div id="motor-block" class="flex-col hidden gap-2 text-xs font-medium text-primary">
                <div class="flex items-center gap-1">
                    <img class="size-4" src="{{ asset('assets/images/engine.svg') }}" alt="running">
                    @lang('messages.block_motor') <span id="motor-block-state" class="font-semibold uppercase"></span>
                </div>

                <div class="flex items-center gap-2">
                    <button @click="$dispatch('ignition-on', { imei: selectedDevice })"
                        class="flex items-center justify-center text-white transition-all duration-300 rounded-full bg-emerald-500 size-8 hover:bg-emerald-600 focus:ring focus:ring-emerald-600 ring-offset-1">
                        <img class="size-4" src="{{ asset('assets/images/play-white.svg') }}" alt="play">
                    </button>
                    <button @click="$dispatch('ignition-off', { imei: selectedDevice })"
                        class="flex items-center justify-center text-white transition-all duration-300 rounded-full bg-rose-500 size-8 hover:bg-rose-600 focus:ring focus:ring-rose-600 ring-offset-1">
                        <img class="size-4" src="{{ asset('assets/images/pause.svg') }}" alt="play">
                    </button>
                </div>
            </div>


            <div class="flex items-center gap-4 mt-5 2xl:mt-8 ms-auto">
                @if ($user->role == 'admin' || $user->role == 'operator' || $user->role == 'technician')
                    <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                        <a id="route-history" href="#" @mouseenter="showTooltip = true"
                            @mouseleave="showTooltip = false">
                            <img class="-mt-1 size-6" src="{{ asset('assets/images/route.svg') }}" alt="test">
                        </a>
                        <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                            x-transition:enter-start="opacity-0 transform scale-95"
                            x-transition:enter-end="opacity-100 transform scale-100"
                            x-transition:leave="transition ease-in duration-150"
                            x-transition:leave-start="opacity-100 transform scale-100"
                            x-transition:leave-end="opacity-0 transform scale-95"
                            class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                            style="display: none;">
                            @lang('messages.device_history')

                        </div>
                    </div>
                @endif

                <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                    <button id="create-geofence-btn" @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                        <img class="size-6" src="{{ asset('assets/images/magic.svg') }}" alt="draw">
                    </button>
                    <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                        x-transition:enter-start="opacity-0 transform scale-95"
                        x-transition:enter-end="opacity-100 transform scale-100"
                        x-transition:leave="transition ease-in duration-150"
                        x-transition:leave-start="opacity-100 transform scale-100"
                        x-transition:leave-end="opacity-0 transform scale-95"
                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                        style="display: none;" id="create-geofence-btn-text">
                        @lang('messages.activate_alarm')

                    </div>
                </div>


                <button class="close-btn" onclick="closePlayer()">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="size-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                    </svg>
                </button>
                <button id="map-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-5">
                        <path fill-rule="evenodd"
                            d="M8.161 2.58a1.875 1.875 0 0 1 1.678 0l4.993 2.498c.**************.336 0l3.869-1.935A1.875 1.875 0 0 1 21.75 4.82v12.485c0 .71-.401 1.36-1.037 1.677l-4.875 2.437a1.875 1.875 0 0 1-1.676 0l-4.994-2.497a.375.375 0 0 0-.336 0l-3.868 1.935A1.875 1.875 0 0 1 2.25 19.18V6.695c0-.71.401-1.36 1.036-1.677l4.875-2.437ZM9 6a.75.75 0 0 1 .75.75V15a.75.75 0 0 1-1.5 0V6.75A.75.75 0 0 1 9 6Zm6.75 3a.75.75 0 0 0-1.5 0v8.25a.75.75 0 0 0 1.5 0V9Z"
                            clip-rule="evenodd" />
                    </svg>

                    @lang('messages.geofence')
                </button>
                <button id="close-geofence-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="size-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                    </svg>

                    Geofence
                </button>
            </div>
        </div>
        <div>
            <div class="flex flex-wrap items-start justify-center gap-3 mt-3 md:-translate-y-3">
                <div id="movementStatus">
                </div>
                <div id="ignitionStatus">
                </div>
                <div id="engineStatus">
                </div>
                <div id="gpsStatus">
                </div>
                <div id="deviceStatus">
                </div>
                <div class="mt-1">
                    <div class="flex items-center">
                        <div
                            class="relative flex items-center w-12 h-4 gap-2 overflow-hidden bg-gray-200 rounded-md shrink-0">
                            <div id="battery-bar"
                                class="h-full font-bold text-center text-white transition-all duration-500"
                                style="width: 0%; background-color: red;">
                            </div>

                        </div>
                        <span id="battery-text" class="text-xs font-medium ms-1"></span>

                    </div>
                </div>
            </div>
            <div class="flex items-center justify-center mt-5 md:mt-0">
                <span class="text-sm font-medium">
                    {{ __('messages.dash_history_mode') }}
                </span>
                <label class="toggle-switch">
                    <input type="checkbox" id="liveMode" checked>
                    <div class="toggle-switch-background">
                        <div class="toggle-switch-handle"></div>
                    </div>
                </label>
                <span class="text-sm font-medium">
                    {{ __('messages.dash_live_mode') }}
                </span>


            </div>


            <div x-data="{ show: window.innerWidth >= 768 }" @resize.window="show = window.innerWidth >= 768" class="mt-3 md:mt-0">
                <!-- Toggle Button -->
                <button @click="show = !show"
                    class="flex items-center w-full pb-1 text-sm font-bold transition border-b-[1.5px] text-primary border-b-gray-300">
                    <span x-text="show ? '@lang('messages.hide_details')' : '@lang('messages.show_details')'"></span>
                    <span><img class="transition-all duration-300 size-5 ms-2"
                            x-bind:class="show ? 'rotate-180' : 'rotate-0'" src="{{ asset('assets/images/down.svg') }}"
                            alt="down"></span>
                </button>

                <!-- Device Info -->
                <div id="deviceInfo" x-show="show" x-cloak x-transition>
                </div>
            </div>



        </div>
        <div id="historyControls" style="display: none;">
            <div class="flex flex-wrap items-center justify-center gap-4 md:flex-nowrap">
                <select id="dateDropdown"
                    class="peer py-2 px-3 block w-fit min-w-36 border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"></select>

                <div class="flex items-center gap-2">
                    <div id="seek-container">
                        <span id="currentTime" class="md:whitespace-nowrap"></span>
                        <input type="range" id="seekbar" min="0" max="100" value="0">
                    </div>

                    <button onclick="playPause()"
                        class="flex items-center justify-center px-4 py-2 text-sm text-white font-medium bg-[#450099] rounded-lg shadow-lg transition duration-300 ease-in-out transform hover:bg-[#5c0db4] hover:scale-105">

                        <svg id="playIcon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                            fill="currentColor" class="size-5 me-2">
                            <path fill-rule="evenodd"
                                d="M4.5 5.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L7.28 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653Z"
                                clip-rule="evenodd" />
                        </svg>

                        <svg id="pauseIcon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                            fill="currentColor" class="hidden size-6 me-2">
                            <path fill-rule="evenodd"
                                d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12ZM9 8.25a.75.75 0 0 0-.75.75v6c0 .414.336.75.75.75h.75a.75.75 0 0 0 .75-.75V9a.75.75 0 0 0-.75-.75H9Zm5.25 0a.75.75 0 0 0-.75.75v6c0 .414.336.75.75.75H15a.75.75 0 0 0 .75-.75V9a.75.75 0 0 0-.75-.75h-.75Z"
                                clip-rule="evenodd" />
                        </svg>

                        <span id="buttonText">{{ __('messages.play') }}<span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div id="toast-container"></div>

    @push('scripts')
        @include('livewire.panel.dashboard-scripts')
    @endpush


    <livewire:panel.ignition-on-off />
</main>
