   <script>
       let map = L.map('worldMap').setView([41.8719, 12.5674], 5);
       <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);

       // Add the easyPrint control
       var printPlugin = L.easyPrint({
           title: '@lang('messages.export_png')', // Tooltip text (this will not be shown in automatic trigger)
           position: 'topleft', // Button position on the map
           exportOnly: true, // Only export the map, not print
           filename: 'map_snapshot', // Set the filename for the export
           sizeModes: ['A4Landscape'], // Use the current size of the map
       }).addTo(map);


       let markers = {};
       let selectedDevice = null;
       let historyPath = null;
       let historyMarker = null;
       let playInterval = null;
       let currentHistoryIndex = 0;
       let historyData = [];

       // New variables for geofence functionality
       let drawnItems = new L.FeatureGroup();
       let drawControl;
       let isDrawControlVisible = false;
       let currentGeofence = null;
       let currentGeofenceId = null;
       let selectedImei = null;
       let isGeofenceMode = false;
       let isAdmin = @json($user->role == 'admin');

       map.addLayer(drawnItems);


       var selectedVehicleType = "default";
       var vehicleTypeCache = {}; // Cache to store vehicle types by IMEI
       var vehicleTypeCacheNumberPlate = {}; // Cache to store vehicle types by IMEI
       var deviceModel = {}; // Cache to store vehicle types by IMEI
       var BlockMotorEnabled = {}; // Cache to store vehicle types by IMEI
       var vehicleClient = {}; // Cache to store vehicle types by IMEI
       var vehicleModel = {};
       var vehicleBrand = {};
       var initialOdometerCache = {}; // Cache to store vehicle types by IMEI

       function getDevicePin(ignitionStatus, movementStatus, imei, speed, callback) {
           if (imei) {
               // Check if the vehicle type for this IMEI is already cached
               if (vehicleTypeCache[imei]) {
                   // Use the cached vehicle type
                   let vehicleType = vehicleTypeCache[imei] || 'default';
                   let color = getPinColor(ignitionStatus, movementStatus, speed);
                   let iconUrl = `{{ asset('assets/images/${vehicleType}/${color}.svg') }}`;
                   callback(iconUrl);
               } else {
                   // If not cached, fetch from the API
                   $.ajax({
                       url: "{{ url('/') }}/api/fetch-device-type",
                       type: "POST",
                       data: {
                           imei: imei
                       },
                       dataType: "json",
                       success: function(data) {
                           let vehicleType = data?.device?.vehicle_type || 'default';
                           let vehicleNumberPlate = data?.device?.number_plate || null;
                           let model = data?.device?.model || null;
                           let blockMotor = data?.device?.motor_block_enabled;
                           let initialOdometer = data?.device?.initial_odometer || 0;
                           let client = data?.device?.client?.user?.name || null;
                           let brand = data?.device?.vehicle_brand || null;
                           let deviceVehicleModel = data?.device?.vehicle_model || null;
                           if (data?.device?.client?.type == 'private') {
                               client = data?.device?.client?.last_name + ' ' + client
                           }
                           vehicleTypeCache[imei] = vehicleType; // Cache the fetched vehicle type
                           vehicleTypeCacheNumberPlate[imei] =
                               vehicleNumberPlate; // Cache the fetched vehicle type

                           deviceModel[imei] =
                               model; // Cache the fetched vehicle type

                           BlockMotorEnabled[imei] =
                               blockMotor; // Cache the fetched vehicle type

                           vehicleClient[imei] =
                               client; // Cache the fetched vehicle type

                           vehicleBrand[imei] =
                               brand; // Cache the fetched vehicle type


                           vehicleModel[imei] =
                               deviceVehicleModel; // Cache the fetched vehicle type

                           initialOdometerCache[imei] =
                               initialOdometer; // Cache the fetched vehicle type


                           let color = getPinColor(ignitionStatus, movementStatus, speed);
                           let iconUrl = `{{ asset('assets/images/${vehicleType}/${color}.svg') }}`;
                           callback(iconUrl);
                       },
                       error: function(xhr, status, error) {
                           console.error("Error fetching device data:", error);
                           callback(null); // Pass null if there's an AJAX error
                       }
                   });
               }
           } else {
               // Handle cases where imei is not provided
               let color = getPinColor(ignitionStatus, movementStatus, speed);
               let iconUrl = `{{ asset('assets/images/${selectedVehicleType}/${color}.svg') }}`;
               callback(iconUrl);
           }
       }

       // Function to determine the pin color
       function getPinColor(ignitionStatus, movementStatus, speed = 0) {
           if (movementStatus == 1 && ignitionStatus == 1 && speed > 0) {
               return "green";
           } else if (ignitionStatus == 1 && movementStatus == 0) {
               return "yellow";
           } else {
               if (movementStatus == 1 && speed > 0) {
                   return "green";
               } else {
                   return "red";
               }
           }
       }


       function checkAndAssignIMEI(model, imei) {
           let boatImei = null;

           // Check if the model contains the word "bot" (case-insensitive)
           if (model.toLowerCase().includes("boat")) {
               boatImei = imei;
           }

           return boatImei;
       }

       var updateCount = 0;
       var boatImiei = null;


       const dealerDevices = @json($dealerDevices);

       let role = `{{ $user->role }}`;

       if (dealerDevices && dealerDevices.length > 0 || role == 'admin' || role == 'operator' || role == 'technician') {
           function updateLiveData() {
               fetch(`{{ url('/') }}/data/live/live_data.json?nocache=${new Date().getTime()}`)
                   .then(response => response.json())
                   .then(data => {


                       for (let imei in data) {
                           // Check if the device should be displayed
                           if (dealerDevices && dealerDevices.length > 0 && !dealerDevices.includes(imei)) {
                               continue; // Skip devices not in the dealer's list
                           }


                           let device = data[imei];
                           if (device.latitude && device.longitude &&
                               !isNaN(device.latitude) && !isNaN(device.longitude)) {
                               let lat = parseFloat(device.latitude);
                               let lng = parseFloat(device.longitude);
                               if (lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {

                                   // Usage example:
                                   let ignitionStatus = device["239"]; // 1 for 'On', 0 for 'Off'
                                   let movementStatus = device["240"]; // 1 for 'On', 0 for 'Off'
                                   let speed = device["speed"] ?? 0;
                                   let deviceIMEI = imei;


                                   // Call getDevicePin with a callback
                                   getDevicePin(ignitionStatus, movementStatus, deviceIMEI, speed, function(iconUrl) {
                                       if (!iconUrl) {
                                           // Set a fallback icon if iconUrl is null
                                           iconUrl = "{{ asset('assets/images/default/default-pin.svg') }}";
                                       }


                                       if (boatImiei == null) {
                                           boatImiei = checkAndAssignIMEI(iconUrl, imei);
                                       }


                                       let vehicleNumberPlate = vehicleTypeCacheNumberPlate[deviceIMEI] ||
                                           null; // Get number plate

                                       let icon = L.divIcon({
                                           className: 'custom-marker',
                                           html: `
            <div style="position: relative; text-align: center;">
                <img src="${iconUrl}" style="width: 40px; height: 80px;">
                ${vehicleNumberPlate ? `<div style="position: absolute;top: 60px; left: 50%;transform: translateX(-50%);background: rgba(0, 0, 0, 0.7);color: white;font-size: 12px;padding: 2px 5px;border-radius: 10px;white-space: nowrap;">${vehicleNumberPlate}</div>` : ''}
            </div>
        `,
                                           iconSize: [40, 80],
                                           iconAnchor: [20, 80]
                                       });

                                       // Check if marker exists for the given IMEI before setting the icon
                                       if (markers[deviceIMEI]) {
                                           markers[deviceIMEI].setLatLng([lat, lng]);
                                           markers[deviceIMEI].setIcon(icon);
                                       } else {
                                           markers[deviceIMEI] = L.marker([lat, lng], {
                                                   icon: icon
                                               })
                                               .addTo(map)
                                               .on('click', () => openPlayer(imei));
                                       }
                                   });


                               } else {
                                   console.warn(`Invalid coordinates for device ${imei}: ${lat}, ${lng}`);
                               }



                           } else {
                               // console.warn(`Missing or invalid coordinates for device ${imei}`);
                           }
                       }

                       // updateCount = updateCount + 1;


                       let currentSelectedDevice = data[selectedDevice];
                       if (currentSelectedDevice) {
                           if (document.getElementById('device_lng_lat')) {
                               document.getElementById('device_lng_lat').innerHTML =
                                   `Lat: ${currentSelectedDevice.longitude} / Long: ${currentSelectedDevice.latitude}`;
                           }

                           if (document.getElementById('last_update')) {
                               document.getElementById('last_update').innerHTML =
                                   `${currentSelectedDevice.last_update}`;
                           }

                           function kilometersPerHourToKnots(kmh) {
                               return kmh / 1.852;
                           }

                           // Example usage
                           let speedInKmPerHour = currentSelectedDevice.speed; // Assuming speed is in km/h
                           let speedInKnots = kilometersPerHourToKnots(speedInKmPerHour).toFixed(
                               2); // Round to 2 decimal places

                           // Update the speed display based on the selected device
                           if (vehicleTypeCache[selectedDevice] == 'boat') {
                               if (document.getElementById('speed')) {
                                   document.getElementById('speed').innerHTML = `${speedInKnots} knots`;
                               }
                           } else {
                               if (document.getElementById('speed')) {
                                   document.getElementById('speed').innerHTML = `${currentSelectedDevice.speed} km/h`;
                               }
                           }

                           if (document.getElementById('voltage') && currentSelectedDevice['66']) {
                               document.getElementById('voltage').innerHTML =
                                   `${currentSelectedDevice['66'].toFixed(1)}V`;
                           }



                           function metersToNauticalMiles(meters) {
                               return meters / 1852;
                           }

                           // Example usage
                           let distanceInMeters = currentSelectedDevice[
                               '16']; // Assuming '16' represents distance in meters
                           let nauticalMiles = metersToNauticalMiles(distanceInMeters).toFixed(
                               2); // Round to 2 decimal places

                           // Update the distance display based on the selected device
                           if (vehicleTypeCache[selectedDevice] == 'boat') {
                               if (document.getElementById('distance')) {
                                   document.getElementById('distance').innerHTML =
                                       `${nauticalMiles} @lang('messages.nautical_miles')`;
                               }
                           } else {
                               if (document.getElementById('distance')) {
                                   document.getElementById('distance').innerHTML =
                                       `${((Number(distanceInMeters || 0) / 1000) + (Number(initialOdometerCache[selectedDevice]) || 0)).toFixed(2)} km`;
                               }
                           }



                           updateDeviceStatus(currentSelectedDevice);

                           const gsmSignalValue = currentSelectedDevice['21'];
                           displaySignalStrength(gsmSignalValue);
                       }
                   })
                   .catch(error => {
                       console.error('Error fetching live data:', error);
                   });
           }
       } else {
           function updateLiveData() {
               return false
           }
       }


       function openPlayer(imei) {
           selectedDevice = imei;
           selectedImei = imei;
           document.getElementById('player').style.display = 'flex';
           document.getElementById('deviceInfo').innerText = `Device IMEI: ${imei}`;
           map.flyTo(markers[imei].getLatLng(), 16);
           updatePlayerInfo();
           if (isGeofenceMode) {
               fetchGeofences(imei);
           } else {
               fetchAutomaticGeofence(imei);
           }

           if (BlockMotorEnabled[imei] == 1) {
               document.getElementById('motor-block').classList.add('flex')
               document.getElementById('motor-block').classList.remove('hidden')
               checkBlockMotorState(imei)
           } else {
               document.getElementById('motor-block').classList.add('hidden')
               document.getElementById('motor-block').classList.remove('flex')
           }


           if (imei != null && document.getElementById('route-history') != null) {
               // Construct the URL dynamically in JavaScript
               const historyUrl = `{{ url('device-history/${imei}') }}`;
               document.getElementById('route-history').setAttribute('href', historyUrl);
           }
       }

       function checkBlockMotorState(imei) {
           if (imei != null) {
               fetch(`{{ url('/') }}/api/motor-block-state/${imei}`)
                   .then(response => response.json())
                   .then(data => {
                       let stateStatus = document.getElementById('motor-block-state');
                       if (stateStatus && data && data.state) {


                           if (data.state == 'ON') {
                               stateStatus.innerText = `@lang('messages.on')`;

                               stateStatus.classList.add('text-emerald-600');
                               stateStatus.classList.remove('text-red-500');
                           } else {
                               stateStatus.innerText = `@lang('messages.off')`;

                               stateStatus.innerText = 'OFF';
                               stateStatus.classList.remove('text-emerald-600');
                               stateStatus.classList.add('text-red-500');
                           }
                       }

                   }).catch(error => {
                       console.error('Error fetching live data:', error);
                   });;
           }

       }

       function closePlayer() {
           document.getElementById('player').style.display = 'none';
           map.flyTo([41.8719, 12.5674], 5);
           if (historyPath) {
               map.removeLayer(historyPath);
               historyPath = null;
           }
           if (historyMarker) {
               map.removeLayer(historyMarker);
               historyMarker = null;
           }

           clearGeofence()
       }

       async function getAddressFromCoordinates(lat, lng) {
           let url =
               `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=it`;

           try {
               const response = await fetch(url, {
                   headers: {
                       'User-Agent': 'MeMove/1.0 (<EMAIL>)' // Set a custom user-agent
                   }
               });

               if (!response.ok) {
                   throw new Error(`HTTP error! Status: ${response.status}`);
               }

               const data = await response.json();

               if (data && data.display_name) {
                   return data.display_name; // Return the address
               } else {
                   console.error('No address found for the given coordinates.');
                   return null;
               }
           } catch (error) {
               console.error('Error fetching address:', error);
               return null;
           }
       }



       function updatePlayerInfo() {
           let liveMode = document.getElementById('liveMode').checked;
           document.getElementById('historyControls').style.display = liveMode ? 'none' : 'block';

           if (liveMode) {

               // Clear history paths if they exist
               if (historyPath) {
                   map.removeLayer(historyPath);
                   historyPath = null;
               }
               if (historyMarker) {
                   map.removeLayer(historyMarker);
                   historyMarker = null;
               }


               // Update with live data
               fetch(`{{ url('/') }}/data/live/live_data.json?nocache=${new Date().getTime()}`)
                   .then(response => response.json())
                   .then(data => {

                       let device = data[selectedDevice];


                       let lat = device.latitude;
                       let lng = device.longitude;


                       var address = 'Fetching address...';

                       let htmlContent = `
                                <ul class="grid gap-1 mt-5 space-y-2 text-xs gap-x-3 text-primary md:grid-cols-2 lg:grid-cols-3">
                             <!-- IMEI -->
                            <li class="flex items-center col-span-2 md:col-span-1">
                                <img src="assets/images/hashtag-primary.svg" alt="hashtag icon" class="w-5 h-5 mr-2">
                                <span>${selectedDevice}</span>
                          <div style="transform:rotateX(180deg);scale: 0.6;"  id="signalStrength" class="flex ml-2 w-fit"></div> 
    
                      
                            </li>
    `;
                       if (deviceModel[selectedDevice] != null) {

                           htmlContent += ` <li class="flex items-center col-span-2 md:col-span-1">
<img src="{{ asset('assets/images/device.svg') }}" alt="hashtag icon" class="w-5 h-4 mr-2">
<span>${deviceModel[selectedDevice]}</span>

</li>`;
                       }
                       if (vehicleTypeCacheNumberPlate[selectedDevice] != null) {

                           htmlContent += ` <li class="flex items-center col-span-2 gap-1 md:col-span-1">
                                    <div class="flex items-center"><img src="{{ asset('assets/images/plate.png') }}" alt="hashtag icon" class="w-5 h-4 mr-2">
            <span>${vehicleTypeCacheNumberPlate[selectedDevice]}</span></div>
        </li>`;
                       }
                       if (vehicleModel[selectedDevice] != null || vehicleBrand[selectedDevice] != null) {

                           htmlContent += ` <li class="flex items-center gap-1">
                                      <div class="flex items-center col-span-2 md:col-span-1"><img src="{{ asset('assets/images/steering-wheel.svg') }}" alt="hashtag icon" class="mr-2 size-5">
            <span>${vehicleBrand[selectedDevice]} ${vehicleModel[selectedDevice]}</span>
            </div>
        </li>`;
                       }

                       if (vehicleClient[selectedDevice] != null) {

                           htmlContent += ` <li class="flex items-center col-span-2 md:col-span-1">
            <img src="{{ asset('assets/images/device-user.svg') }}" alt="user" class="w-5 h-4 mr-2">
            <span>${vehicleClient[selectedDevice]}</span>
        </li>`;
                       }
                       htmlContent += `
                                <!-- Coordinates -->
                                <li class="flex items-center col-span-2 md:col-span-1">
                                    <img src="assets/images/pin.svg" alt="device ID icon" class="w-5 h-5 mr-2">
                                    <span id="device_lng_lat">Lat: ${device.longitude} / Long: ${device.latitude}</span>
                                </li>
    
                                <!-- Speed -->
                                <li class="flex items-center col-span-2 md:col-span-1">
                                    <img src="assets/images/speed.svg" alt="device ID icon" class="w-5 h-5 mr-2">
                                    <span id="speed">${device.speed} km/h</span>
                                </li>

                                <!-- Speed -->
                                <li class="flex items-center col-span-2 md:col-span-1">
                                    <img src="assets/images/distance.svg" alt="device ID icon" class="w-5 h-5 mr-2">
                                    <span id="distance">
  ${(
    (Number(device['16']) || 0) / 1000 +
    (Number(initialOdometerCache[selectedDevice]) || 0)
  ).toFixed(2)} km
</span>
                                </li>


                                <li class="flex items-center col-span-2 md:col-span-1">
                                    <img src="assets/images/voltage.svg" alt="device ID icon" class="w-5 h-5 mr-2">
                                    <span id="voltage">${device['66'].toFixed(1)} V</span>
                                </li>
    
                              <!-- Address -->
                             <li class="flex items-center col-span-2">
                                <img src="assets/images/location.svg" alt="device ID icon" class="w-5 h-5 mr-2">
                                <span id="address">${device['address'] || 'N/A'}</span>
                            </li>

                                <!-- Last Update -->
                                <li class="flex col-span-2 md:col-span-1">
                                    <img src="assets/images/history.svg" alt="device ID icon" class="w-5 h-5 mr-2">
                                    <span id="last_update">${device.last_update}</span>
                                </li>


                            </ul>
                            `;

                       document.getElementById('deviceInfo').innerHTML = htmlContent;

                       updateDeviceStatus(device);

                       const gsmSignalValue = device['21'];
                       displaySignalStrength(gsmSignalValue);





                   });
           } else {
               // Load available dates
               // Load available dates
               fetch(`{{ url('/') }}/data/history/${selectedDevice}/dates.json`)
                   .then(response => response.json())
                   .then(dates => {
                       // Sort the dates in chronological order
                       // dates.sort((a, b) => {
                       //     // Convert DD-MM-YYYY to YYYY-MM-DD for comparison
                       //     let [dayA, monthA, yearA] = a.split('-').map(Number);
                       //     let [dayB, monthB, yearB] = b.split('-').map(Number);
                       //     let dateA = new Date(yearA, monthA - 1, dayA);
                       //     let dateB = new Date(yearB, monthB - 1, dayB);
                       //     return dateA - dateB; // Ascending order
                       // });

                       let dropdown = document.getElementById('dateDropdown');
                       dropdown.innerHTML = '';
                       dates.forEach(date => {
                           let option = document.createElement('option');
                           option.value = date;
                           option.textContent = date;
                           dropdown.appendChild(option);
                       });
                       loadHistoryData(dates[0]); // Load the first date's data
                   });




               // Update with live data
               fetch(`{{ url('/') }}/data/live/live_data.json?nocache=${new Date().getTime()}`)
                   .then(response => response.json())
                   .then(data => {

                       let device = data[selectedDevice];


                       let lat = device.latitude;
                       let lng = device.longitude;


                       var address = 'Fetching address...';

                       document.getElementById('deviceInfo').innerHTML = `
                                <ul class="grid gap-1 mt-5 space-y-2 text-xs gap-x-3 text-primary md:grid-cols-2">
                             <!-- IMEI -->
                            <li class="flex items-center">
                                <img src="assets/images/hashtag-primary.svg" alt="hashtag icon" class="w-5 h-5 mr-2">
                                <span>${selectedDevice} </span>
                          <div style="transform:rotateX(180deg);scale: 0.6;"  id="signalStrength" class="flex ml-2 w-fit"></div> 
                            </li>
    
                               
                            <!-- Address -->
                             <li class="flex items-center">
                                <img src="assets/images/location.svg" alt="device ID icon" class="w-5 h-5 mr-2">
                                <span id="address">${device?.address || 'N/A'}</span>
                            </li>
    
                                <!-- Coordinates -->
                                <li class="flex items-center">
                                    <img src="assets/images/pin.svg" alt="device ID icon" class="w-5 h-5 mr-2">
                                    <span id="device_lng_lat">Lat: ${device.longitude} / Long: ${device.latitude}</span>
                                </li>
    
    
                                <!-- Speed -->
                                <li class="flex items-center">
                                    <img src="assets/images/speed.svg" alt="device ID icon" class="w-5 h-5 mr-2">
                                    <span id="speed">${device.speed} km/h</span>
                                </li>
                                <!-- Speed -->
                                <li class="flex items-center">
                                    <img src="assets/images/distance.svg" alt="device ID icon" class="w-5 h-5 mr-2">
                                    <span id="distance">
  ${(
    (Number(device['16']) || 0) / 1000 +
    (Number(initialOdometerCache[selectedDevice]) || 0)
  ).toFixed(2)} km
</span>
                                </li>
    
                                <!-- Last Update -->
                                <li class="flex">
                                    <img src="assets/images/history.svg" alt="device ID icon" class="w-5 h-5 mr-2">
                                    <span id="last_update">${device.last_update}</span>
                                </li>


                                <li class="flex items-center">
                                    <img src="assets/images/voltage.svg" alt="device ID icon" class="w-5 h-5 mr-2">
                                    <span id="voltage">${device['66'].toFixed(1)} V</span>
                                </li>
                            </ul>
                            `;

                       updateDeviceStatus(device);

                       const gsmSignalValue = device['21'];
                       displaySignalStrength(gsmSignalValue);



                   });
           }
       }

       function displaySignalStrength(signalValue) {
           if (document.getElementById('signalStrength')) {
               const signalStrengthContainer = document.getElementById('signalStrength');
               signalStrengthContainer.innerHTML = ''; // Clear previous bars

               for (let i = 1; i <= 5; i++) {
                   const bar = document.createElement('div');
                   bar.style.width = '6px'; // Width of each bar
                   bar.style.margin = '0 1px'; // Space between bars
                   bar.style.backgroundColor = i <= signalValue ? 'green' : 'lightgray'; // Color of the bars
                   bar.style.height = `${(i * 5)}px`; // Height based on signal strength
                   signalStrengthContainer.appendChild(bar);
               }
           }
       }

       // Function to update movement status based on device.240
       function updateDeviceStatus(device) {
           if (document.getElementById('movementStatus') && document.getElementById('gpsStatus') && document
               .getElementById('deviceStatus')) {

               const movementStatusElement = document.getElementById('movementStatus');
               const gpsStatusElement = document.getElementById('gpsStatus');
               const deviceStatusElement = document.getElementById('deviceStatus');
               const ignitionElement = document.getElementById('ignitionStatus');
               const engineElement = document.getElementById('engineStatus');
               var batteryLevel = device["113"] ?? 0;
               var batteryBar = document.getElementById('battery-bar');
               var batteryText = document.getElementById('battery-text');
               // var batteryStatus = document.getElementById('battery-status');


               let voltage = parseFloat(device["66"] || 0); // External Voltage value
               let speed = parseFloat(device["speed"] || 0); // Speed value

               if (device["239"] == 1 && speed > 0 && device["240"] == 1) {
                   // Engine Running - Alternator charging
                   engineElement.innerHTML = `
            <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-teal-100 text-teal-800 rounded-full">
                <img class="size-4" src="{{ asset('assets/images/engine-on.svg') }}" alt="running">
                {{ __('messages.on') }}
            </span>
        `;
               } else {
                   // Engine OFF
                   engineElement.innerHTML = `
            <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-rose-100 text-rose-800 rounded-full">
                <img class="size-4" src="{{ asset('assets/images/engine-off.svg') }}" alt="stopped">
                {{ __('messages.off') }}
            </span>
        `;
               }


               if (device["239"] == 1) { // Ignition is ON
                   // Engine Running - Alternator charging
                   ignitionElement.innerHTML = `
            <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-teal-100 text-teal-800 rounded-full">
                <img class="size-4" src="{{ asset('assets/images/ignition-on.svg') }}" alt="running">
                {{ __('messages.on') }}
            </span>
        `;

               } else {

                   // Engine OFF
                   ignitionElement.innerHTML = `
            <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-rose-100 text-rose-800 rounded-full">
                <img class="size-4" src="{{ asset('assets/images/ignition-off.svg') }}" alt="stopped">
                {{ __('messages.off') }}
            </span>
        `;
               }

               // Movement Status Logic
               if (device["240"] == 1 && device["speed"] > 0) {
                   // Moving - Show green badge
                   movementStatusElement.innerHTML = `
        <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-teal-100 text-teal-800 rounded-full">
            <img class="size-4" src="{{ asset('assets/images/running.svg') }}" alt="running">
            {{ __('messages.moving') }}
        </span>
    `;
               } else {
                   // Stopped - Show red badge
                   movementStatusElement.innerHTML = `
        <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-rose-100 text-rose-800 rounded-full">
            <img class="size-3" src="{{ asset('assets/images/stopped.svg') }}" alt="stopped">
             {{ __('messages.stopped') }}
        </span>
    `;
               }

               // Update GPS Signal Status
               switch (device["69"]) {
                   case 0:
                       gpsStatusElement.innerHTML = `
                    <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-red-100 text-red-800 rounded-full">
                    <svg class="size-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M2 12C2 12.3853 2.31236 12.6977 2.69767 12.6977H4.59041C4.92078 16.2509 7.74914 19.0792 11.3023 19.4096V21.3023C11.3023 21.6876 11.6147 22 12 22C12.3853 22 12.6977 21.6876 12.6977 21.3023V19.4096C16.2509 19.0792 19.0792 16.2509 19.4096 12.6977H21.3023C21.6876 12.6977 22 12.3853 22 12C22 11.6147 21.6876 11.3023 21.3023 11.3023H19.4096C19.0792 7.74914 16.2509 4.92078 12.6977 4.59041V2.69767C12.6977 2.31236 12.3853 2 12 2C11.6147 2 11.3023 2.31236 11.3023 2.69767V4.59041C7.74914 4.92078 4.92078 7.74914 4.59041 11.3023H2.69767C2.31236 11.3023 2 11.6147 2 12ZM8.51163 12C8.51163 10.0734 10.0734 8.51163 12 8.51163C13.9266 8.51163 15.4884 10.0734 15.4884 12C15.4884 13.9266 13.9266 15.4884 12 15.4884C10.0734 15.4884 8.51163 13.9266 8.51163 12Z" fill="currentColor"></path> <path d="M9.90698 12C9.90698 10.8441 10.8441 9.90698 12 9.90698C13.1559 9.90698 14.093 10.8441 14.093 12C14.093 13.1559 13.1559 14.093 12 14.093C10.8441 14.093 9.90698 13.1559 9.90698 12Z" fill="currentColor"></path> </g></svg>
                        @lang('messages.gps_off')
                    </span>
                `;
                       break;
                   case 1:
                       gpsStatusElement.innerHTML = `
                    <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-teal-100 text-teal-800 rounded-full">
                            <svg class="size-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M2 12C2 12.3853 2.31236 12.6977 2.69767 12.6977H4.59041C4.92078 16.2509 7.74914 19.0792 11.3023 19.4096V21.3023C11.3023 21.6876 11.6147 22 12 22C12.3853 22 12.6977 21.6876 12.6977 21.3023V19.4096C16.2509 19.0792 19.0792 16.2509 19.4096 12.6977H21.3023C21.6876 12.6977 22 12.3853 22 12C22 11.6147 21.6876 11.3023 21.3023 11.3023H19.4096C19.0792 7.74914 16.2509 4.92078 12.6977 4.59041V2.69767C12.6977 2.31236 12.3853 2 12 2C11.6147 2 11.3023 2.31236 11.3023 2.69767V4.59041C7.74914 4.92078 4.92078 7.74914 4.59041 11.3023H2.69767C2.31236 11.3023 2 11.6147 2 12ZM8.51163 12C8.51163 10.0734 10.0734 8.51163 12 8.51163C13.9266 8.51163 15.4884 10.0734 15.4884 12C15.4884 13.9266 13.9266 15.4884 12 15.4884C10.0734 15.4884 8.51163 13.9266 8.51163 12Z" fill="currentColor"></path> <path d="M9.90698 12C9.90698 10.8441 10.8441 9.90698 12 9.90698C13.1559 9.90698 14.093 10.8441 14.093 12C14.093 13.1559 13.1559 14.093 12 14.093C10.8441 14.093 9.90698 13.1559 9.90698 12Z" fill="currentColor"></path> </g></svg>
                        @lang('messages.gps_signal_good')
                    </span>
                `;
                       break;
                   case 2:
                       gpsStatusElement.innerHTML = `
                    <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-yellow-100 text-yellow-800 rounded-full">
                            <svg class="size-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M2 12C2 12.3853 2.31236 12.6977 2.69767 12.6977H4.59041C4.92078 16.2509 7.74914 19.0792 11.3023 19.4096V21.3023C11.3023 21.6876 11.6147 22 12 22C12.3853 22 12.6977 21.6876 12.6977 21.3023V19.4096C16.2509 19.0792 19.0792 16.2509 19.4096 12.6977H21.3023C21.6876 12.6977 22 12.3853 22 12C22 11.6147 21.6876 11.3023 21.3023 11.3023H19.4096C19.0792 7.74914 16.2509 4.92078 12.6977 4.59041V2.69767C12.6977 2.31236 12.3853 2 12 2C11.6147 2 11.3023 2.31236 11.3023 2.69767V4.59041C7.74914 4.92078 4.92078 7.74914 4.59041 11.3023H2.69767C2.31236 11.3023 2 11.6147 2 12ZM8.51163 12C8.51163 10.0734 10.0734 8.51163 12 8.51163C13.9266 8.51163 15.4884 10.0734 15.4884 12C15.4884 13.9266 13.9266 15.4884 12 15.4884C10.0734 15.4884 8.51163 13.9266 8.51163 12Z" fill="currentColor"></path> <path d="M9.90698 12C9.90698 10.8441 10.8441 9.90698 12 9.90698C13.1559 9.90698 14.093 10.8441 14.093 12C14.093 13.1559 13.1559 14.093 12 14.093C10.8441 14.093 9.90698 13.1559 9.90698 12Z" fill="currentColor"></path> </g></svg>
                        @lang('messages.gps_signal_weak')
                    </span>
                `;
                       break;
                   case 3:
                       gpsStatusElement.innerHTML = `
                    <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-gray-100 text-gray-800 rounded-full">
                            <svg class="size-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M2 12C2 12.3853 2.31236 12.6977 2.69767 12.6977H4.59041C4.92078 16.2509 7.74914 19.0792 11.3023 19.4096V21.3023C11.3023 21.6876 11.6147 22 12 22C12.3853 22 12.6977 21.6876 12.6977 21.3023V19.4096C16.2509 19.0792 19.0792 16.2509 19.4096 12.6977H21.3023C21.6876 12.6977 22 12.3853 22 12C22 11.6147 21.6876 11.3023 21.3023 11.3023H19.4096C19.0792 7.74914 16.2509 4.92078 12.6977 4.59041V2.69767C12.6977 2.31236 12.3853 2 12 2C11.6147 2 11.3023 2.31236 11.3023 2.69767V4.59041C7.74914 4.92078 4.92078 7.74914 4.59041 11.3023H2.69767C2.31236 11.3023 2 11.6147 2 12ZM8.51163 12C8.51163 10.0734 10.0734 8.51163 12 8.51163C13.9266 8.51163 15.4884 10.0734 15.4884 12C15.4884 13.9266 13.9266 15.4884 12 15.4884C10.0734 15.4884 8.51163 13.9266 8.51163 12Z" fill="currentColor"></path> <path d="M9.90698 12C9.90698 10.8441 10.8441 9.90698 12 9.90698C13.1559 9.90698 14.093 10.8441 14.093 12C14.093 13.1559 13.1559 14.093 12 14.093C10.8441 14.093 9.90698 13.1559 9.90698 12Z" fill="currentColor"></path> </g></svg>
                      @lang('messages.gps_inactive')
                    </span>
                `;
                       break;
               }

               // Update Device Status
               switch (device["200"]) {
                   case 0:
                       deviceStatusElement.innerHTML = `
                    <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-teal-100 text-teal-800 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-3">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5h3m-6.75 2.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-15a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 4.5v15a2.25 2.25 0 0 0 2.25 2.25Z" />
                    </svg>
    
                    {{ __('messages.active') }}
                    </span>
                `;
                       break;
                   case 1:
                       deviceStatusElement.innerHTML = `
                    <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-blue-100 text-blue-800 rounded-full">
                       <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-3">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5h3m-6.75 2.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-15a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 4.5v15a2.25 2.25 0 0 0 2.25 2.25Z" />
                    </svg>
                      @lang('messages.gps_inactive')
                    </span>
                `;
                       break;
                   case 2:
                       deviceStatusElement.innerHTML = `
                    <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-gray-200 text-gray-600 rounded-full">
                       <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-3">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5h3m-6.75 2.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-15a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 4.5v15a2.25 2.25 0 0 0 2.25 2.25Z" />
                    </svg>
                         @lang('messages.low_power_mode')
                    </span>
                `;
                       break;
                   case 3:
                       deviceStatusElement.innerHTML = `
                    <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-lightblue-100 text-lightblue-800 rounded-full">
                       <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-3">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5h3m-6.75 2.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-15a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 4.5v15a2.25 2.25 0 0 0 2.25 2.25Z" />
                    </svg>
                         @lang('messages.low_power_online')
                    </span>
                `;
                       break;
                   case 4:
                       deviceStatusElement.innerHTML = `
                    <span class="py-1 px-2 inline-flex items-center gap-x-1 text-[10px] font-medium bg-gray-400 text-gray-800 rounded-full">
                       <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-3">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5h3m-6.75 2.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-15a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 4.5v15a2.25 2.25 0 0 0 2.25 2.25Z" />
                    </svg>
                        @lang('messages.battery_saver_mode')
                    </span>
                `;
                       break;
               }
           }


           if (batteryLevel != null) {
               // Ensure the value is between 0 and 100
               const level = Math.max(0, Math.min(100, batteryLevel));
               batteryBar.style.display = 'block';

               // Set bar width
               batteryBar.style.width = `${level}%`;

               // Set color based on the level
               if (level < 20) {
                   batteryBar.style.backgroundColor = 'red';
               } else if (level < 50) {
                   batteryBar.style.backgroundColor = '#faab34';
                   batteryBar.style.color = 'black'; // Ensure text is readable
               } else {
                   batteryBar.style.backgroundColor = 'green';
               }

               // Display level percentage
               batteryText.textContent = `${level}%`;



               // Battery status message
               // if (level < 20) {
               //     batteryStatus.textContent = 'Battery is critically low! ⚠️';
               // } else if (level < 50) {
               //     batteryStatus.textContent = 'Battery is below 50%. Charge soon.';
               // } else {
               //     batteryStatus.textContent = 'Battery is good. ✅';
               // }

           } else {
               batteryBar.style.display = 'hidden';
               // batteryStatus.textContent = 'Battery level not found for this device.';

           }

       }


       function loadHistoryData(date) {
           fetch(`{{ url('/') }}/data/history/${selectedDevice}/${date}.json`)
               .then(response => response.json())
               .then(data => {

                   // Filter out points with latitude and longitude equal to 0
                   historyData = data.filter(point => point.latitude !== 0 || point.longitude !== 0);

                   if (historyPath) {
                       map.removeLayer(historyPath);
                   }
                   if (historyMarker) {
                       map.removeLayer(historyMarker);
                   }

                   let path = historyData.map(point => [point.latitude, point.longitude]);

                   // Check if path has valid points before proceeding
                   if (path.length > 0) {
                       historyPath = L.polyline(path, {
                           color: '#450099'
                       }).addTo(map);
                       map.fitBounds(historyPath.getBounds());
                       historyMarker = L.marker(path[0]).addTo(map);

                       let ignitionStatus = historyData[0]["239"]; // 1 for 'On', 0 for 'Off'
                       let movementStatus = historyData[0]["240"]; // 1 for 'On', 0 for 'Off'
                       let speed = historyData[0]["speed"]; // Speed value

                       // Call getDevicePin with a callback
                       getDevicePin(ignitionStatus, movementStatus, null, speed, function(iconUrl) {
                           if (!iconUrl) {
                               // Set a fallback icon if iconUrl is null
                               iconUrl = "{{ asset('assets/images/default/default-pin.svg') }}";
                           }

                           let icon = L.icon({
                               iconUrl: iconUrl,
                               iconSize: [40, 80], // Adjust the size as needed
                               iconAnchor: [12, 41] // Adjust the anchor point as needed
                           });

                           historyMarker.setIcon(icon);
                       });
                   } else {
                       console.warn('No valid points to display on the map.');
                   }

                   currentHistoryIndex = 0;

                   updateSeekbar();
               })
               .catch(error => {
                   console.error('Error loading history data:', error);
               });
       }

       let isPlaying = false;

       function playPause() {
           if (playInterval) {
               clearInterval(playInterval);
               playInterval = null;
               isPlaying = false;
               document.getElementById('playIcon').classList.remove('hidden');
               document.getElementById('pauseIcon').classList.add('hidden');
               document.getElementById('buttonText').textContent = "{{ __('messages.play') }}"
           } else {
               playInterval = setInterval(moveHistoryMarker, 100);
               isPlaying = true;
               document.getElementById('playIcon').classList.add('hidden');
               document.getElementById('pauseIcon').classList.remove('hidden');
               document.getElementById('buttonText').textContent = "{{ __('messages.pause') }}"
           }
       }


       function moveHistoryMarker() {
           if (currentHistoryIndex < historyData.length - 1) {
               currentHistoryIndex++;
               let point = historyData[currentHistoryIndex];
               historyMarker.setLatLng([point.latitude, point.longitude]);


               let ignitionStatus = point["239"]; // 1 for 'On', 0 for 'Off'
               let movementStatus = point["240"]; // 1 for 'On', 0 for 'Off'
               let speed = point["speed"]; // 1 for 'On', 0 for 'Off'


               // Call getDevicePin with a callback
               getDevicePin(ignitionStatus, movementStatus, null, speed, function(iconUrl) {
                   if (!iconUrl) {
                       // Set a fallback icon if iconUrl is null
                       iconUrl = "{{ asset('assets/images/default/default-pin.svg') }}";
                   }

                   let icon = L.icon({
                       iconUrl: iconUrl,
                       iconSize: [40, 80], // Adjust the size as needed
                       iconAnchor: [12, 41] // Adjust the anchor point as needed
                   });



                   historyMarker.setIcon(icon);
               });


               document.getElementById('device_lng_lat').innerHTML =
                   `Lat: ${point.longitude} / Long: ${point.latitude}`;
               document.getElementById('address').innerHTML = `${point.address || 'N/A'}`;
               document.getElementById('voltage').innerHTML = `${point['66'].toFixed(1)}V`;
               document.getElementById('last_update').innerHTML = `${point.last_update}`;


               if (vehicleTypeCache[selectedDevice] == 'boat') {
                   // Convert meters per second to knots
                   let metersPerSecond = point.speed;
                   let knots = metersPerSecond * 1.94384; // 1 m/s = 1.94384 knots

                   // Convert meters to nautical miles
                   let distance = point['16'] / 1852; // 1 nautical mile = 1852 meters

                   document.getElementById('speed').innerHTML = `${knots.toFixed(2)} knots`;
                   document.getElementById('distance').innerHTML = `${distance.toFixed(2)} NM`;
               } else {
                   // Convert meters to kilometers and display in HTML
                   document.getElementById('speed').innerHTML = `${point.speed} km/h`;
                   document.getElementById('distance').innerHTML =
                       `${((Number(point['16'] || 0) / 1000) + (Number(initialOdometerCache[selectedDevice]) || 0)).toFixed(2)} km`;
               }


               updateSeekbar();
           } else {
               clearInterval(playInterval);
               playInterval = null;
           }
       }

       function updateSeekbar() {
           let seekbar = document.getElementById('seekbar');
           seekbar.value = (currentHistoryIndex / (historyData.length - 1)) * 100;

           let fullTime = historyData[currentHistoryIndex].last_update;
           document.getElementById('currentTime').innerText = fullTime;
       }

       document.getElementById('liveMode').addEventListener('change', updatePlayerInfo);

       document.getElementById('dateDropdown').addEventListener('change', (e) => loadHistoryData(e.target.value));
       document.getElementById('seekbar').addEventListener('input', (e) => {
           currentHistoryIndex = Math.floor((e.target.value / 100) * (historyData.length - 1));
           let point = historyData[currentHistoryIndex];
           historyMarker.setLatLng([point.latitude, point.longitude]);

           document.getElementById('device_lng_lat').innerHTML =
               `Lat: ${point.longitude} / Long: ${point.latitude}`;
           document.getElementById('address').innerHTML = `${point.address || 'N/A'}`;
           document.getElementById('voltage').innerHTML = `${point['66'].toFixed(1)}V`;
           document.getElementById('last_update').innerHTML = `${point.last_update}`;


           if (vehicleTypeCache[selectedDevice] == 'boat') {
               // Convert meters per second to knots
               let metersPerSecond = point.speed;
               let knots = metersPerSecond * 1.94384; // 1 m/s = 1.94384 knots

               // Convert meters to nautical miles
               let distance = point['16'] / 1852; // 1 nautical mile = 1852 meters

               document.getElementById('speed').innerHTML = `${knots.toFixed(2)} knots`;
               document.getElementById('distance').innerHTML = `${distance.toFixed(2)} NM`;
           } else {
               // Convert meters to kilometers and display in HTML
               document.getElementById('speed').innerHTML = `${point.speed} km/h`;
               document.getElementById('distance').innerHTML =
                   `${((Number(point['16'] || 0) / 1000) + (Number(initialOdometerCache[selectedDevice]) || 0)).toFixed(2)} km`;
           }



           let ignitionStatus = point["239"]; // 1 for 'On', 0 for 'Off'
           let movementStatus = point["240"]; // 1 for 'On', 0 for 'Off'
           let speed = point["speed"]; // 1 for 'On', 0 for 'Off'


           // Call getDevicePin with a callback
           getDevicePin(ignitionStatus, movementStatus, null, speed, function(iconUrl) {
               if (!iconUrl) {
                   // Set a fallback icon if iconUrl is null
                   iconUrl = "{{ asset('assets/images/default/default-pin.svg') }}";
               }

               let icon = L.icon({
                   iconUrl: iconUrl,
                   iconSize: [40, 80], // Adjust the size as needed
                   iconAnchor: [12, 41] // Adjust the anchor point as needed
               });



               historyMarker.setIcon(icon);
           });


           updateSeekbar();

           updateDeviceStatus(point);

           const gsmSignalValue = point['21'];
           displaySignalStrength(gsmSignalValue);


       });

       function showToast(message) {
           const toastContainer = document.getElementById('toast-container');
           const toast = document.createElement('div');
           toast.className = 'toast';
           toast.textContent = message;
           toastContainer.appendChild(toast);
           setTimeout(() => {
               toast.classList.add('show');
           }, 100);
           setTimeout(() => {
               toast.classList.remove('show');
               setTimeout(() => {
                   toastContainer.removeChild(toast);
               }, 300);
           }, 3000);
       }

       function createDrawControl() {
           return new L.Control.Draw({
               edit: {
                   featureGroup: drawnItems,
                   remove: true
               },
               draw: {
                   polygon: true,
                   rectangle: true,
                   circle: true,
                   circlemarker: false,
                   marker: false,
                   polyline: false
               }
           });
       }

       function initializeDrawControl() {
           if (!drawControl) {
               drawControl = new L.Control.Draw({
                   edit: {
                       featureGroup: drawnItems,
                       remove: true
                   },
                   draw: {
                       polygon: true,
                       rectangle: true,
                       circle: true,
                       circlemarker: false,
                       marker: false,
                       polyline: false
                   }
               });
           }
       }

       function toggleGeofenceMode() {
           isGeofenceMode = !isGeofenceMode;
           if (isGeofenceMode) {
               document.getElementById('create-geofence-btn').style.display = 'none';
               document.getElementById('map-btn').style.display = 'none';
               document.getElementById('close-geofence-btn').style.display = 'flex';
               map.addControl(drawControl);
               drawnItems.clearLayers();

               fetchGeofences(selectedImei);
           } else {
               document.getElementById('create-geofence-btn').style.display = 'block';
               document.getElementById('map-btn').style.display = 'flex';
               document.getElementById('close-geofence-btn').style.display = 'none';

               map.removeControl(drawControl);
               drawnItems.clearLayers();
               if (document.getElementById('liveMode').checked === false) {
                   document.getElementById('liveMode').checked = true;
                   updatePlayerInfo();
               }

               fetchAutomaticGeofence(selectedImei);

           }
       }

       if (document.getElementById('map-btn')) {
           document.getElementById('map-btn').addEventListener('click', toggleGeofenceMode);
       }
       if (document.getElementById('close-geofence-btn')) {
           document.getElementById('close-geofence-btn').addEventListener('click', toggleGeofenceMode);
       }

       if (document.getElementById('create-geofence-btn')) {
           document.getElementById('create-geofence-btn').addEventListener('click', createCircleGeofence);
       }


       function createCircleGeofence() {
           if (!selectedImei || !markers[selectedImei]) {
               alert('No device selected.');
               return;
           }

           // Get device location
           const deviceLocation = markers[selectedImei].getLatLng();

           // Create a 100m radius circle
           currentGeofence = L.circle(deviceLocation, {
               color: 'blue',
               fillColor: '#1E90FF',
               fillOpacity: 0.4,
               radius: 100 // 100 meters
           }).addTo(drawnItems);

           // Save the geofence
           saveGeofenceCircle();
       }



       function toggleDrawControl() {
           initializeDrawControl();
           if (isDrawControlVisible) {
               map.removeControl(drawControl);
           } else {
               map.addControl(drawControl);
           }
           isDrawControlVisible = !isDrawControlVisible;
       }

       function clearGeofence() {
           drawnItems.clearLayers();
           currentGeofence = null;
           currentGeofenceId = null;
           showSaveButton();
       }

       function saveGeofence() {
           if (!currentGeofence || !selectedImei) return;

           let type, coordinates;
           if (currentGeofence instanceof L.Polygon) {
               type = 'polygon';
               coordinates = currentGeofence.getLatLngs()[0].map(latlng => [latlng.lat, latlng.lng]);
           } else if (currentGeofence instanceof L.Rectangle) {
               type = 'rectangle';
               const bounds = currentGeofence.getBounds();
               coordinates = [
                   [bounds.getNorthWest().lat, bounds.getNorthWest().lng],
                   [bounds.getNorthEast().lat, bounds.getNorthEast().lng],
                   [bounds.getSouthEast().lat, bounds.getSouthEast().lng],
                   [bounds.getSouthWest().lat, bounds.getSouthWest().lng]
               ];
           } else if (currentGeofence instanceof L.Circle) {
               type = 'circle';
               const center = currentGeofence.getLatLng();
               const radius = currentGeofence.getRadius();
               coordinates = [center.lat, center.lng, radius];
           }

           const geofenceData = {
               id: currentGeofenceId,
               imei: selectedImei,
               type: type,
               coordinates: JSON.stringify(coordinates)
           };




           fetch('/api/save-geofence', {
                   method: 'POST',
                   headers: {
                       'Content-Type': 'application/json',
                   },
                   body: JSON.stringify(geofenceData),
               })
               .then(response => response.json())
               .then(data => {
                   if (!data.error) {
                       showToast("@lang('messages.geofence_saved_success')");
                       currentGeofenceId = data.id;
                       fetchGeofences(selectedImei);
                   } else {
                       showToast(data.error);
                       currentGeofence = null;
                       currentGeofenceId = null;
                   }
               })
               .catch((error) => {
                   console.error('Error:', error);
                   showToast('Failed to save geofence. Please try again.');
               });
       }

       function saveGeofenceCircle() {
           if (!currentGeofence || !selectedImei) return;

           let type, coordinates;
           if (currentGeofence instanceof L.Circle) {
               type = 'circle';
               const center = currentGeofence.getLatLng();
               const radius = currentGeofence.getRadius();
               coordinates = [center.lat, center.lng, radius];
           }

           const geofenceData = {
               id: currentGeofenceId,
               imei: selectedImei,
               coordinates: JSON.stringify(coordinates)
           };


           fetch('/api/save-automatic-geofence', {
                   method: 'POST',
                   headers: {
                       'Content-Type': 'application/json',
                   },
                   body: JSON.stringify(geofenceData),
               })
               .then(response => response.json())
               .then(data => {
                   if (!data.error) {
                       showToast(data?.message);
                       currentGeofenceId = data?.id;
                       fetchAutomaticGeofence(selectedImei);
                   } else {
                       showToast(data.error);
                       currentGeofence = null;
                       currentGeofenceId = null;
                   }
               })
               .catch((error) => {
                   console.error('Error:', error);
                   showToast('Failed to save geofence. Please try again.');
               });
       }

       if (L.Draw) {
           map.on(L.Draw.Event.CREATED, function(event) {
               const layer = event.layer;
               drawnItems.addLayer(layer);
               currentGeofence = layer;
               currentGeofenceId = null;
               saveGeofence();
           });

           map.on(L.Draw.Event.DELETED, function(event) {
               const layers = event.layers;
               layers.eachLayer(function(layer) {
                   if (layer === currentGeofence) {
                       deleteGeofence();
                   }
               });
           });

           map.on(L.Draw.Event.EDITED, function(event) {
               const layers = event.layers;
               layers.eachLayer(function(layer) {
                   currentGeofence = layer;
                   saveGeofence();
               });
           });
           L.drawLocal = {
               draw: {
                   toolbar: {
                       actions: {
                           title: "{{ __('messages.geofence_cancel_drawing_title') }}",
                           text: "{{ __('messages.geofence_cancel_drawing_text') }}"
                       },
                       finish: {
                           title: "{{ __('messages.geofence_finish_drawing_title') }}",
                           text: "{{ __('messages.geofence_finish_drawing_text') }}"
                       },
                       undo: {
                           title: "{{ __('messages.geofence_delete_last_point_title') }}",
                           text: "{{ __('messages.geofence_delete_last_point_text') }}"
                       },
                       buttons: {
                           polyline: "{{ __('messages.geofence_draw_polyline') }}",
                           polygon: "{{ __('messages.geofence_draw_polygon') }}",
                           rectangle: "{{ __('messages.geofence_draw_rectangle') }}",
                           circle: "{{ __('messages.geofence_draw_circle') }}",
                           marker: "{{ __('messages.geofence_draw_marker') }}",
                           circlemarker: "{{ __('messages.geofence_draw_circlemarker') }}"
                       }
                   },
                   handlers: {
                       circle: {
                           tooltip: {
                               start: "{{ __('messages.geofence_circle_tooltip_start') }}"
                           },
                           radius: "{{ __('messages.geofence_radius') }}"
                       },
                       circlemarker: {
                           tooltip: {
                               start: "{{ __('messages.geofence_circlemarker_tooltip_start') }}"
                           }
                       },
                       marker: {
                           tooltip: {
                               start: "{{ __('messages.geofence_marker_tooltip_start') }}"
                           }
                       },
                       polygon: {
                           tooltip: {
                               start: "{{ __('messages.geofence_polygon_tooltip_start') }}",
                               cont: "{{ __('messages.geofence_polygon_tooltip_cont') }}",
                               end: "{{ __('messages.geofence_polygon_tooltip_end') }}"
                           }
                       },
                       polyline: {
                           error: "{{ __('messages.geofence_polyline_error') }}",
                           tooltip: {
                               start: "{{ __('messages.geofence_polyline_tooltip_start') }}",
                               cont: "{{ __('messages.geofence_polyline_tooltip_cont') }}",
                               end: "{{ __('messages.geofence_polyline_tooltip_end') }}"
                           }
                       },
                       rectangle: {
                           tooltip: {
                               start: "{{ __('messages.geofence_rectangle_tooltip_start') }}"
                           }
                       },
                       simpleshape: {
                           tooltip: {
                               end: "{{ __('messages.geofence_simpleshape_tooltip_end') }}"
                           }
                       }
                   }
               },
               edit: {
                   toolbar: {
                       actions: {
                           save: {
                               title: "{{ __('messages.geofence_save_changes_title') }}",
                               text: "{{ __('messages.geofence_save_changes_text') }}"
                           },
                           cancel: {
                               title: "{{ __('messages.geofence_cancel_editing_title') }}",
                               text: "{{ __('messages.geofence_cancel_editing_text') }}"
                           },
                           clearAll: {
                               title: "{{ __('messages.geofence_clear_all_layers_title') }}",
                               text: "{{ __('messages.geofence_clear_all_layers_text') }}"
                           }
                       },
                       buttons: {
                           edit: "{{ __('messages.geofence_edit_layers') }}",
                           editDisabled: "{{ __('messages.geofence_edit_disabled') }}",
                           remove: "{{ __('messages.geofence_remove_layers') }}",
                           removeDisabled: "{{ __('messages.geofence_remove_disabled') }}"
                       }
                   },
                   handlers: {
                       edit: {
                           tooltip: {
                               text: "{{ __('messages.geofence_edit_tooltip_text') }}",
                               subtext: "{{ __('messages.geofence_edit_tooltip_subtext') }}"
                           }
                       },
                       remove: {
                           tooltip: {
                               text: "{{ __('messages.geofence_remove_tooltip_text') }}"
                           }
                       }
                   }
               }
           };
       } else {
           console.error('Leaflet.draw plugin is not loaded properly');
       }


       function showSaveButton() {
           let saveBtn = document.getElementById('save-geofence-btn');
           if (!saveBtn) {
               saveBtn = document.createElement('button');
               saveBtn.id = 'save-geofence-btn';
               saveBtn.className = 'leaflet-control-button';
               saveBtn.innerHTML = '💾';
               saveBtn.style.position = 'absolute';
               saveBtn.style.top = '80px';
               saveBtn.style.left = '10px';
               document.querySelector('.leaflet-top.leaflet-left').appendChild(saveBtn);
           }
           saveBtn.onclick = currentGeofenceId ? deleteGeofence : saveGeofence;
           saveBtn.title = currentGeofenceId ? 'Delete Geofence' : 'Save Geofence';
       }

       function deleteGeofence() {
           if (!currentGeofenceId || !selectedImei) return;

           fetch('api/delete-geofence', {
                   method: 'POST',
                   headers: {
                       'Content-Type': 'application/json',
                   },
                   body: JSON.stringify({
                       id: currentGeofenceId,
                       imei: selectedImei
                   }),
               })
               .then(response => response.json())
               .then(data => {
                   showToast('Geofence deleted successfully!');
                   currentGeofence = null;
                   currentGeofenceId = null;
                   fetchGeofences(selectedImei);
               })
               .catch((error) => {
                   console.error('Error:', error);
                   showToast('Failed to delete geofence. Please try again.');
               });
       }

       function fetchGeofences(imei) {
           fetch(`{{ url('/') }}/api/get-geofence?imei=${imei}`)
               .then(response => response.json())
               .then(data => {
                   let geofences = data?.geofences || [];
                   drawnItems.clearLayers();
                   if (geofences.length > 0) {

                       geofences.forEach(geofence => {
                           const coordinates = JSON.parse(geofence.coordinates);
                           let layer;
                           if (geofence.type == 'polygon' || geofence.type == 'rectangle') {
                               layer = L.polygon(coordinates).addTo(drawnItems);
                           } else if (geofence.type === 'circle') {
                               layer = L.circle(coordinates.slice(0, 2), {
                                   radius: coordinates[2]
                               }).addTo(drawnItems);
                           }
                           if (layer) {
                               layer.bindPopup(`Geofence ID: ${geofence.id}`);
                               layer.on('click', () => {
                                   currentGeofence = layer;
                                   currentGeofenceId = geofence.id;
                               });
                           }
                       });
                       currentGeofenceId = geofences[0].id;
                       currentGeofence = drawnItems.getLayers()[0];
                       map.fitBounds(drawnItems.getBounds()); // Fit map to geofence bounds
                   }
               })
               .catch(error => console.error('Error fetching geofences:', error));
       }

       function fetchAutomaticGeofence(imei) {
           fetch(`{{ url('/') }}/api/get-automatic-geofence?imei=${imei}`)
               .then(response => response.json())
               .then(data => {
                   let geofences = data.geofences;
                   drawnItems.clearLayers();
                   geofences.forEach(geofence => {
                       const coordinates = JSON.parse(geofence.coordinates);
                       let layer;
                       if (geofence.type == 'polygon' || geofence.type == 'rectangle') {
                           layer = L.polygon(coordinates).addTo(drawnItems);
                       } else if (geofence.type === 'circle') {
                           layer = L.circle(coordinates.slice(0, 2), {
                               radius: coordinates[2]
                           }).addTo(drawnItems);
                       }
                       if (layer) {
                           layer.bindPopup(`Geofence ID: ${geofence.id}`);
                           layer.on('click', () => {
                               currentGeofence = layer;
                               currentGeofenceId = geofence.id;
                           });


                       }

                   });
                   if (geofences.length > 0) {
                       currentGeofenceId = geofences[0].id;
                       currentGeofence = drawnItems.getLayers()[0];
                       map.fitBounds(drawnItems.getBounds()); // Fit map to geofence bounds
                       document.getElementById('create-geofence-btn-text').innerText = `@lang('messages.deactivate_alarm')`;
                       document.querySelector('#create-geofence-btn img').src =
                           `{{ asset('assets/images/alarm-red.svg') }}`;
                   } else {
                       document.getElementById('create-geofence-btn-text').innerText = `@lang('messages.activate_alarm')`;
                       document.querySelector('#create-geofence-btn img').src =
                           `{{ asset('assets/images/alarm-blue.svg') }}`;

                   }
               })
               .catch(error => console.error('Error fetching geofences:', error));
       }

       function updateDrawControl() {
           if (isDrawControlVisible) {
               map.removeControl(drawControl);
               drawControl = createDrawControl();
               map.addControl(drawControl);
           }
       }

       // Initialize geofence controls
       initializeDrawControl();

       setInterval(updateLiveData, 3000); // Update every second
       updateLiveData(); // Initial update

       // Function to get the IMEI from the URL query parameters
       function getIMEIFromURL() {
           const urlParams = new URLSearchParams(window.location.search);
           const imei = urlParams.get('imei'); // Assuming the IMEI is passed like ?imei=3525925734631234
           return imei;
       }

       // Check if there's an IMEI in the URL and call openPlayer with it
       document.addEventListener('DOMContentLoaded', function() {
           const imeiFromURL = getIMEIFromURL();

           if (imeiFromURL) {
               setTimeout(() => {
                   // If IMEI exists in the URL, call openPlayer
                   openPlayer(imeiFromURL);
               }, 1000);
           }
       });
   </script>
