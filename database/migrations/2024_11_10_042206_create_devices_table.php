<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('devices', function (Blueprint $table) {
            $table->id();

            $table->foreignId('dealer_id')->nullable()->constrained('dealers')->nullOnDelete()->noActionOnUpdate();
            $table->foreignId('client_id')->nullable()->constrained('clients')->nullOnDelete()->noActionOnUpdate();

            $table->string('imei')->unique();
            $table->string('model')->nullable();
            $table->string('vehicle_model')->nullable();
            $table->string('vehicle_brand')->nullable();
            $table->string('number_plate')->nullable();
            $table->string('iccid')->nullable();
            $table->string('imsi')->nullable();
            $table->string('initial_odometer')->nullable();
            $table->string('vehicle_type')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_tested')->default(false);
            $table->boolean('is_verified')->default(false);
            $table->boolean('motor_block_enabled')->default(true);
            $table->boolean('in_maintenance')->default(false);
            $table->boolean('ain1_enabled')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('devices');
    }
};
