<?php

namespace App\Livewire\Panel;

use App\Models\Command;
use App\Models\Device;
use Illuminate\Support\Facades\File;
use Livewire\Component;
use Livewire\WithPagination;

class CommandQueue extends Component
{
    use WithPagination;
    public $imei;
    public $command;
    public $custom_command;
    public $search;
    public $status;
    public $openModal = false;
    public $deleteRecordId;

    public $commands = [
        'other' => 'Other',
        'setdigout 1' => 'Turn ON lock',
        'setdigout 0' => 'Turn OFF lock',
        'cpureset' => 'Reset device',
        'web_connect' => 'Force FOTA web connection',
        'getinfo' => 'Get system info',
        'getver' => 'Get code version, IMEI, and more',
        'getstatus' => 'Get modem status',
        'getgps' => 'Get current GPS data',
        'getio' => 'Read IO data',
        'ggps' => 'Get location with Google Maps link',
        'fwstats' => 'Get firmware stats',
        'getrecord' => 'Save and send high priority record',
        'getimeiccid' => 'Get IMEI and ICCID',
        'getimsi' => 'Get SIM IMSI number',
        'getops' => 'List visible operators',
        'allver' => 'Get hardware and firmware versions',
        'countrecs' => 'Count records',
        'deleterecords' => 'Delete all SD card records',
        'cleardigoutprio' => 'Clear DOUT priority',
        'setigndigout' => 'Set DOUT when ignition is off',
        'battery' => 'Get battery state info',
        'wdlog' => 'Get watchdog info',
        'defaultcfg' => 'Load default configuration',
        'sdformat' => 'Format SD card',
    ];

    public function mount($imei = null)
    {
        $this->commands = [
            'other' => __('messages.other'),
            'setdigout 1' => __('messages.setdigout_1'),
            'setdigout 0' => __('messages.setdigout_0'),
            'cpureset' => __('messages.cpureset'),
            'web_connect' => __('messages.web_connect'),
            'getinfo' => __('messages.getinfo'),
            'getver' => __('messages.getver'),
            'getstatus' => __('messages.getstatus'),
            'getgps' => __('messages.getgps'),
            'getio' => __('messages.getio'),
            'ggps' => __('messages.ggps'),
            'fwstats' => __('messages.fwstats'),
            'getrecord' => __('messages.getrecord'),
            'getimeiccid' => __('messages.getimeiccid'),
            'getimsi' => __('messages.getimsi'),
            'getops' => __('messages.getops'),
            'allver' => __('messages.allver'),
            'countrecs' => __('messages.countrecs'),
            'deleterecords' => __('messages.deleterecords'),
            'cleardigoutprio' => __('messages.cleardigoutprio'),
            'setigndigout' => __('messages.setigndigout'),
            'battery' => __('messages.battery'),
            'wdlog' => __('messages.wdlog'),
            'defaultcfg' => __('messages.defaultcfg'),
            'sdformat' => __('messages.sdformat'),
        ];
        $this->imei = $imei;
        if ($imei) {
            $this->openModal = true;
        }
    }


    public function addCommand()
    {
        // Validate input
        $this->validate([
            'imei' => 'required',
            'command' => 'required|max:255',
        ]);

        if ($this->command == 'other') {
            $this->validate([
                'custom_command' => 'required|max:255',
            ]);
        }
        // Check if the command already exists in the queue for the given IMEI
        $existingCommand = Command::where('imei', $this->imei)
            ->where('command', $this->command == 'other' ? $this->custom_command : $this->command)
            ->where('added_in_queue', 1)
            ->whereNull('response')
            ->exists();

        if ($existingCommand) {
            $this->dispatch('notice', type: 'error', text: __('messages.command_already_in_queue'));
            return;
        }

        // Save command to the database
        Command::create([
            'imei' => $this->imei,
            'command' =>  $this->command == 'other' ? $this->custom_command : $this->command,
            'name' => ($this->command == 'other' ? $this->custom_command : $this->commands[$this->command]) ?? '',
        ]);

        // Update the command queue JSON file
        $this->updateCommandQueue();

        $this->dispatch('notice', type: 'success', text: __('messages.command_added_in_queue'));
        $this->dispatch('close-modal');
        $this->reset('command', 'custom_command');
        $this->openModal = false;
    }

    private function updateCommandQueue()
    {
        // Fetch commands that should be added to the queue
        $commands = Command::whereNull('response')
            ->where('added_in_queue', '!=', 2) // Exclude commands marked as deleted
            ->get();

        $queue = [];
        foreach ($commands as $cmd) {
            $queue[$cmd->imei][] = [
                'command' => $cmd->command,
            ];
        }

        $path = public_path('command/queue.json');
        File::ensureDirectoryExists(dirname($path));
        File::put($path, json_encode($queue, JSON_PRETTY_PRINT));

        // Mark commands as added to queue (only those not already deleted)
        Command::whereNull('response')
            ->where('added_in_queue', 0)
            ->update(['added_in_queue' => 1]);
    }


    public function deleteRecordConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-record-modal');
    }

    public function deleteRecord()
    {
        if ($this->deleteRecordId) {
            $command = Command::find($this->deleteRecordId);

            if ($command) {
                // Set `added_in_queue` to 2
                $command->update(['added_in_queue' => 2]);

                // Update the command queue JSON file
                $this->updateCommandQueue();

                $this->dispatch('notice', type: 'success', text: __('messages.command_removed'));
            } else {
                $this->dispatch('notice', type: 'error', text: 'Command not found!');
            }

            $this->dispatch('close-modal');
        } else {
            $this->dispatch('notice', type: 'error', text: 'No command ID provided!');
        }
    }



    public function render()
    {
        $devices = Device::when(auth()->user()->role == 'dealer', function ($query) {
            $query->where('dealer_id', auth()->user()->dealer->id ?? null);
        })
            ->when(auth()->user()->role == 'client', function ($query) {
                $query->where('client_id', auth()->user()->client->id ?? null);
            })->pluck('imei', 'imei');

        $deviceCommands = Command::when(isset($this->search), function ($query) {
            $query->where(function ($query) {
                $query->where('command', 'like', '%' . $this->search . '%')
                    ->orWhere('response', 'like', '%' . $this->search . '%');
            });
        })
            ->when(isset($this->status) && $this->status == 1, function ($query) {
                $query->whereNotNull('response_received_at');
            })
            ->latest()
            ->paginate(10);

        return view('livewire.panel.command-queue', compact('devices', 'deviceCommands'));
    }
}
