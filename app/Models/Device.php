<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Device extends Model
{
    protected $fillable = [
        'imei',
        'model',
        'number_plate',
        'vehicle_model',
        'vehicle_brand',
        'iccid',
        'imsi',
        'is_active',
        'dealer_id',
        'client_id',
        'is_tested',
        'is_verified',
        'vehicle_type',
        'motor_block_enabled',
        'initial_odometer',
        'in_maintenance',
        'ain1_enabled',
        'created_at',
        'updated_at',
    ];

    public function dealer()
    {
        return $this->belongsTo(Dealer::class);
    }

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function testResults()
    {
        return $this->hasMany(DeviceTestResult::class)->orderBy('created_at', 'desc');
    }

    public function latestTestResult()
    {
        return $this->hasOne(DeviceTestResult::class)->latestOfMany();
    }

    public function logs()
    {
        return $this->hasMany(DeviceLog::class);
    }

    public function contract()
    {
        return $this->hasOne(Contract::class)->latest();
    }

    public function latestPendingContract()
    {
        return $this->hasOne(Contract::class)
            ->where('status', 'pending')
            ->latest('created_at'); // Ensure it picks the latest pending contract
    }
}
