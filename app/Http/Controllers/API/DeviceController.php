<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Jobs\TriggerGeofenceCall;
use App\Models\Command;
use App\Models\Device;
use App\Models\DeviceLog;
use App\Models\Event;
use App\Models\Notification;
use App\Models\User;
use App\Services\FCMService;
use Barryvdh\DomPDF\Facade\Pdf;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Twilio\Rest\Client;
use Twilio\TwiML\VoiceResponse;

class DeviceController extends Controller
{
    public function fetchDeviceType(Request $request)
    {
        if ($request->imei) {
            $device = Device::with('client:id,user_id,last_name,type', 'client.user:id,name')->where('imei', $request->imei)->first(['imei', 'model', 'vehicle_type', 'number_plate', 'client_id', 'motor_block_enabled', 'vehicle_model', 'vehicle_brand', 'initial_odometer', 'in_maintenance']);

            if ($device?->initial_odometer) {
                $device->initial_odometer = (float) $device->initial_odometer;
            }

            return response()->json(['device' => $device]);
        }
    }

    public function logDeviceEvent(Request $request)
    {
        $imei = $request->get('imei');
        $data = $request->get('data');
        $event_id = $request->get('event_id');

        // Ensure eventID and event_id exists
        if (!isset($data['eventID']) && !isset($event_id)) {
            return response()->json(['status' => 'error', 'message' => 'eventID or event_id not found']);
        }

        $eventID = $data['eventID'] ?? $event_id;
        $eventMessage = null;
        $notificationType = null;
        $notificationTitle = null;

        $device = Device::where('imei', $imei)->first();

        if ($eventID == 9 && $device->ain1_enabled == false) {
            return response()->json(['status' => 'error', 'message' => 'AIN1 not enabled']);
        }

        switch ($eventID) {
            case 9: // AIN 1
                $ain1Status = $data[9] ?? null;
                if ($ain1Status !== null) {
                    if ($ain1Status > 8) {
                        $eventMessage = 'Veicolo in allarme';
                    } else {
                        $eventMessage = 'Allarme veicolo disattivato';
                    }
                    $notificationTitle = 'Allarme AIN1';
                    $notificationType = 'Tampering Alert';
                }
                break;

            case 249: // Jamming
                $jammingStatus = $data[249] ?? null;
                if ($jammingStatus !== null) {
                    $eventMessage = $jammingStatus == 1 ? 'Interferenza iniziata' : 'Interferenza terminata';
                    $notificationTitle = 'Allarme Interferenza';
                    $notificationType = 'Tampering Alert';
                }
                break;

            // case 247: // Crash Detection
            //     $crashStatus = $data[247] ?? null;
            //     $crashMessages = [
            //         1 => 'Incidente reale rilevato (dispositivo calibrato)',
            //         2 => 'Traccia di incidente limitata (dispositivo non calibrato)',
            //         3 => 'Traccia di incidente limitata (dispositivo calibrato)',
            //         4 => 'Traccia completa di incidente (dispositivo non calibrato)',
            //         5 => 'Traccia completa di incidente (dispositivo calibrato)',
            //         6 => 'Incidente reale rilevato (dispositivo non calibrato)',
            //     ];
            //     $eventMessage = $crashMessages[$crashStatus] ?? 'Evento di rilevamento incidente avvenuto';
            //     $notificationTitle = 'Incidente Rilevato';
            //     $notificationType = 'Other Alerts';
            //     break;

            // case 255: // Over Speeding
            //     $overSpeedingValue = $data[255] ?? null;
            //     if ($overSpeedingValue !== null) {
            //         $eventMessage = "Over speeding detected at {$overSpeedingValue} km/h";
            //         $notificationTitle = 'Speed Limit Exceeded';
            //         $notificationType = 'Speed Limit Exceeded';
            //     }
            //     break;

            case 246: // Towing
                $towingStatus = $data[246] ?? null;
                $eventMessage = $towingStatus == 1 ? 'Traino rilevato' : 'Veicolo fermo';
                $notificationTitle = 'Allarme Traino';
                $notificationType = 'Tampering Alert';
                break;

            case 252: // Unplug
                $unplugStatus = $data[252] ?? null;
                $eventMessage = $unplugStatus == 1 ? 'Batteria scollegata' : 'Batteria presente';
                $notificationTitle = 'Stato Batteria';
                $notificationType = 'Power Off';
                break;

            case 318: // GNSS Jamming
                $gnssJammingState = $data[318] ?? null;
                $gnssMessages = [
                    0 => 'Il segnale GPS è stabile, nessuna interferenza rilevata.',
                    1 => 'Interferenza rilevata: la precisione del segnale GPS potrebbe essere compromessa.',
                    2 => 'Problema critico: segnale GPS perso a causa di forte interferenza.',
                ];
                $eventMessage = $gnssMessages[$gnssJammingState] ?? 'Stato di interferenza GNSS sconosciuto rilevato.';
                $notificationTitle = 'Allarme Interferenza GNSS';
                $notificationType = 'Tampering Alert';
                break;

            default:
                return response()->json(['status' => 'error', 'message' => 'Unknown eventID or event_id'], 400);
        }


        // Save the event and notification if a valid message was generated
        if ($eventMessage) {




            $address = $data['address'] ?? null;

            // if (isset($data['longitude'], $data['latitude'])) {
            //     // Fetch street name using Nominatim API
            //     $url = "https://nominatim.openstreetmap.org/reverse?format=json&lat={$data['latitude']}&lon={$data['longitude']}&accept-language=it";

            //     // Set User-Agent
            //     $options = [
            //         "http" => [
            //             "header" => "User-Agent: MeMove/1.0 (<EMAIL>)\r\n"
            //         ]
            //     ];

            //     // Create stream context
            //     $context = stream_context_create($options);

            //     // Fetch the data
            //     $response = @file_get_contents($url, false, $context);

            //     if ($response) {
            //         $json = json_decode($response, true);
            //         $address = $json['display_name'] ?? 'N/A';
            //     }
            // }

            // Save the event
            Event::create([
                'imei' => $imei,
                'event_message' => $eventMessage,
                'longitude' => $data['longitude'] ?? null,
                'latitude' => $data['latitude'] ?? null,
                'address' => $address,
            ]);


            if ($device->in_maintenance == true) {
                return response()->json(['status' => 'error', 'message' => 'Device is in maintenance. Skipping notification.']);
            }

            $userId = $device->client?->user_id ?? null;
            $numberPlate = $device->number_plate ?? $imei;


            // Check if the user has enabled the notification for this event type
            $user = User::find($userId);
            $notificationPermissionField = strtolower(str_replace(' ', '_', $notificationType)); // Example: 'Tampering Alert' -> 'tampering_alert'
            $userPermission = $user->$notificationPermissionField ?? false;


            if ($userPermission == 1) {
                $fcmToken = $user->fcm_token ?? null;

                $detailedContent = "{$eventMessage}. Dispositivo: {$numberPlate}. Posizione: " . ($address ?? 'Coordinate: ' . $data['latitude'] . ', ' . $data['longitude']);

                // Save the notification
                Notification::create([
                    'title' => $notificationTitle,
                    'content' => $detailedContent,
                    'type' => $notificationType,
                    'imei' => $imei,
                    'user_id' => $userId,
                ]);

                if ($fcmToken) {
                    FCMService::sendNotification($fcmToken, $notificationTitle, $detailedContent);
                }
            }

            return response()->json(['status' => 'success', 'message' => $eventMessage]);
        }

        return response()->json(['status' => 'error', 'message' => 'Invalid event data']);
    }


    //  // Check if the event ID is 239 (Ignition)
    //  if (isset($data['eventID']) && $data['eventID'] == 239) {
    //     $ignitionStatus = $data[239] ?? null;

    //     if ($ignitionStatus !== null) {
    //         // Determine the ignition status message
    //         $eventMessage = $ignitionStatus == 1 ? 'Ignition On' : 'Ignition Off';

    //         // Save the event to the database
    //         Event::create([
    //             'imei' => $imei,
    //             'event_message' => $eventMessage,
    //             'longitude' => $data['longitude'] ?? null,
    //             'latitude' => $data['latitude'] ?? null,
    //         ]);
    //     }
    // }
    // // Check if the event ID is 249 (Jamming)
    // else
    public function logDeviceGeofenceEvent(Request $request)
    {
        // Extract data
        $imei = $request->get('imei');

        $location = null;

        $device = Device::where('imei', $imei)->first();



        try {
            if ($request->get('latitude') && $request->get('longitude')) {
                $api = "http://**************/nominatim/reverse?lat={$request->get('latitude')}&lon={$request->get('longitude')}&format=json&addressdetails=1&accept-language=it";

                $response = Http::timeout(5)->get($api);

                if ($response->successful()) {
                    $data = $response->json();
                    $location = $data['display_name'] ?? $request->get('latitude') . ', ' . $request->get('longitude');
                }
            }
        } catch (\Throwable $th) {
            //throw $th;
        }




        $identifier = $device?->number_plate ?? $device?->imei;

        $eventMessage = 'Dispositivo ' . $imei . ' veicolo ' . $identifier . ' è uscito dal geofencing ID: ' . ($request->get('geofence_id') ?? 'N/A') . ' alla posizione ' . $location . ', coordinate: Long:' . ($request->get('longitude') ?? 'N/A') . ' Lat:' . ($request->get('latitude') ?? 'N/A');


        // Save the event to the database
        $event = Event::create([
            'imei' => $imei,
            'event_message' => $eventMessage,
            'longitude' => $request->get('longitude') ?? null,
            'latitude' => $request->get('latitude') ?? null,
            'address' => $location ?? null,
        ]);

        if ($event && $imei) {
            if ($device->in_maintenance == true) {
                return response()->json(['status' => 'error', 'message' => 'Device is in maintenance. Skipping notification.']);
            }


            if ($device) {
                $notificationType = 'Geofence Exit';
                $notificationTitle = 'Allarme Uscita Geofence';
                $notificationMessage = "Il tuo veicolo {$identifier} è uscito dal geofencing.";


                $user = User::find($device?->client?->user_id);


                if ($user && $user->geofence_exit == 1) {
                    // Save the notification
                    $notification = Notification::create([
                        'title' => $notificationTitle,
                        'content' => $notificationMessage,
                        'type' => $notificationType,
                        'imei' => $imei,
                        'user_id' => $user->id ?? null, // Assumes a method to get user_id by IMEI
                        'is_geofence_event' => 1,
                        'response_status' => 'pending',
                    ]);

                    // When a geofence exit occurs, dispatch job to be executed after 1 minute
                    TriggerGeofenceCall::dispatch($notification->id, $event)->delay(now()->addMinute());

                    $fcmToken = $user->fcm_token ?? null;



                    if ($fcmToken) {
                        FCMService::sendNotification($fcmToken, $notificationTitle, $notificationMessage);
                    }


                    // $deviceId = $device->id;
                    // $this->logAction($deviceId, $eventMessage);


                    // if ($device->client && $device->client?->phone_number) {
                    //     $this->triggerAutomatedCall($device->client);
                    // }
                }
            }
        }


        return response()->json(['status' => 'success']);
    }

    public function triggerAutomatedCall($customer)
    {
        $sid = env('TWILIO_SID');
        $token = env('TWILIO_TOKEN');
        $twilio = new Client($sid, $token);

        $twilio->calls->create(
            $customer->phone_number,
            env('TWILIO_PHONE_NUMBER'),
            [
                'url' => route('voice-response') // Handle user response
            ]
        );
    }

    public function handleResponse(Request $request)
    {
        $response = new VoiceResponse();

        $response->say("Allarme: Il tuo veicolo è uscito dal geofencing.");


        $response->gather([
            'numDigits' => 1,
            'action' => route('process-response'),
            'method' => 'POST'
        ])->say("Premi 1 se lo hai spostato. Premi 2 per connetterti con il centro operativo.");

        return response($response)->header('Content-Type', 'text/xml');
    }

    public function processResponse(Request $request)
    {
        $digit = $request->input('Digits');
        $response = new VoiceResponse();

        if ($digit == '1') {
            $response->say("Grazie per aver confermato. Nessuna ulteriore azione è richiesta.");
            // $this->logAction('Customer confirmed they moved the device.');
            $response->hangup(); // Explicitly ends the call
        } elseif ($digit == '2') {
            $response->say("Ti stiamo connettendo con il centro operativo.");
            $response->dial(env('OPERATIONAL_CENTER_PHONE'));
            // $this->logAction('Customer requested operational center contact.');
        } else {
            $response->say("Input non valido. Per favore riprova.");
            $response->hangup(); // Explicitly ends the call
        }

        return response($response)->header('Content-Type', 'text/xml');
    }


    public function logAction($deviceId, $action)
    {
        if ($deviceId) {
            DeviceLog::create([
                'device_id' => $deviceId,
                'log' => $action,
            ]);
        }
    }

    public function saveCommandResponse(Request $request)
    {
        \Log::info('saveCommandResponse: ' . json_encode($request->all()));
        $imei = $request->get('imei');
        $command = $request->get('command');
        $response = $request->get('response') ?? 'No response received';
        if ($imei && $command) {
            $lastCommand = Command::where('imei', $imei)
                ->where('command', $command)
                ->whereNull('response')
                ->where('added_in_queue', 1)
                ->first();

            if ($lastCommand) {
                $lastCommand->update([
                    'response' => $response,
                    'response_received_at' => now(),
                ]);
            } else if ($command == 'command_response') {

                $lastCommandFromDevice = Command::where('imei', $imei)
                    ->whereNull('response')
                    ->where('added_in_queue', 1)
                    ->first();

                if ($lastCommandFromDevice) {
                    $lastCommandFromDevice->update([
                        'response' => $response,
                        'response_received_at' => now(),
                    ]);
                }
            } else {
                Command::create([
                    'imei' => $imei,
                    'command' => $command,
                    'response' => $response,
                    'response_received_at' => now(),
                    'added_in_queue' => 1, // or handle as required
                ]);
            }
        }


        return response()->json(['message' => 'Command response saved successfully.'], 200);
    }


    public function motorBlockState($imei = null)
    {
        if ($imei) {
            $command = Command::where('imei', $imei)->where('command', 'like', '%setdigout%')->whereNotNull('response')->orderBy('response_received_at', 'desc')->first();
            if ($command) {

                if ($command->command == 'setdigout 1') {
                    return jsonResponse(true, ['state' => 'ON']);
                } else {
                    return jsonResponse(false, ['state' => 'OFF']);
                }
            } else {
                return jsonResponse(false, ['state' => 'OFF']);
            }
        } else {
            return jsonResponse(false, ['state' => 'OFF']);
        }
    }

    public function exportPdf(Request $request)
    {
        // Optimize memory and execution time for large datasets
        ini_set('memory_limit', '2048M'); // Increase to 2GB for very large datasets
        ini_set('max_execution_time', 300); // 5 minutes timeout

        $validator = Validator::make($request->all(), [
            'imei' => 'required|string|exists:devices,imei',
            'map_image' => 'nullable|string', // Base64 Image
            'data' => 'required|array'
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        $imei = $request->imei;
        $device = Device::where('imei', $imei)->first();

        if (!$device) {
            return response()->json(['error' => 'Device not found'], 404);
        }

        // Optimize data processing with chunking for large datasets
        $dataCount = count($request->data);
        $totalOdometer = 0;

        if ($dataCount > 0) {
            $firstOdometer = $request->data[0][5] ?? 0;
            $lastOdometer = $request->data[$dataCount - 1][5] ?? 0;
            $totalOdometer = ($lastOdometer - $firstOdometer);
        }

        // Process data in chunks to reduce memory usage
        $chunkSize = 500; // Process 500 records at a time
        $processedHistory = [];

        for ($i = 0; $i < $dataCount; $i += $chunkSize) {
            $chunk = array_slice($request->data, $i, $chunkSize);

            foreach ($chunk as $entry) {
                $processedHistory[] = [
                    'timestamp' => $entry[7] ?? 'N/A',
                    'movement' => $entry[4] ?? 'N/A',
                    'ignition' => $entry[3] ?? 'N/A',
                    'speed' => $entry[2] ?? 'N/A',
                    'odometer' => $entry[5] ?? 'N/A',
                    'address' => $entry[6] ?? 'N/A',
                ];
            }

            // Force garbage collection after each chunk
            if ($i % ($chunkSize * 4) === 0) {
                gc_collect_cycles();
            }
        }

        // Optimize image processing
        $imagePath = null;
        if (!empty($request->map_image)) {
            $imagePath = $this->processMapImage($request->map_image, $imei);
        }

        // Generate PDF with optimized settings
        $pdf = Pdf::loadView('pdf.device-history-optimized', [
            'device_history' => $processedHistory,
            'plate' => $device->number_plate ?? 'N/A',
            'imagePath' => $imagePath,
            'total_distance' => abs($totalOdometer),
            'record_count' => $dataCount,
        ])
            ->setPaper('A4', 'portrait')
            ->setOptions([
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
                'defaultFont' => 'Arial',
                'dpi' => 96, // Lower DPI for faster processing
                'defaultPaperSize' => 'A4',
                'chroot' => public_path(),
            ]);

        // Clean up temporary files
        if ($imagePath && file_exists($imagePath)) {
            register_shutdown_function(function () use ($imagePath) {
                @unlink($imagePath);
            });
        }

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->output();
        }, "{$imei}_history_" . date('d-m-Y') . ".pdf", [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="' . $imei . '_history_' . date('d-m-Y') . '.pdf"',
        ]);
    }

    /**
     * Optimize image processing for better performance
     */
    private function processMapImage($base64Image, $imei)
    {
        try {
            // Create unique filename to avoid conflicts
            $filename = 'map_' . $imei . '_' . time() . '.jpg';
            $imagePath = public_path('exports/' . $filename);

            // Ensure directory exists
            $directory = dirname($imagePath);
            if (!File::exists($directory)) {
                File::makeDirectory($directory, 0755, true, true);
            }

            // Decode and process image more efficiently
            $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64Image));

            if ($imageData === false) {
                return null;
            }

            $image = imagecreatefromstring($imageData);
            if ($image === false) {
                return null;
            }

            // Optimize image size for PDF (reduce dimensions if too large)
            $width = imagesx($image);
            $height = imagesy($image);

            // Resize if image is too large (max 1200px width)
            if ($width > 1200) {
                $newWidth = 1200;
                $newHeight = ($height * $newWidth) / $width;

                $resizedImage = imagecreatetruecolor($newWidth, $newHeight);
                imagecopyresampled($resizedImage, $image, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
                imagedestroy($image);
                $image = $resizedImage;
            }

            // Save with optimized compression
            imagejpeg($image, $imagePath, 60); // Lower quality for smaller file size
            imagedestroy($image);

            return $imagePath;
        } catch (Exception $e) {
            Log::error('Image processing failed: ' . $e->getMessage());
            return null;
        }
    }
}
