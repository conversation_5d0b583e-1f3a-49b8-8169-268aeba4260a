#!/usr/bin/env python3
"""
Test script for Teltonika ping handling implementation.
This script validates the ping detection and handling functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from index import is_ping_packet, handle_ping_packet

def test_ping_detection():
    """Test ping packet detection functionality."""
    print("Testing ping packet detection...")
    
    # Test cases for ping detection
    test_cases = [
        # (data, expected_result, description)
        (b'\xFF', True, "Single byte 0xFF (common ping)"),
        (b'\x00', True, "Single byte 0x00 (common ping)"),
        (b'\x01', True, "Single byte 0x01 (ping)"),
        (b'\xFF\x00', False, "Two bytes (not ping)"),
        (b'', False, "Empty data (not ping)"),
        (b'\x00\x00\x00\x00\x00\x00\x08\x08\x01\x00\x00\x00\x00\x00\x00\x00', False, "IMEI packet (not ping)"),
        (b'\x00\x00\x00\x00\x00\x00\x00\x25\x08\x02', False, "Codec 8 packet start (not ping)"),
    ]
    
    passed = 0
    failed = 0
    
    for data, expected, description in test_cases:
        try:
            result = is_ping_packet(data)
            if result == expected:
                print(f"✓ PASS: {description}")
                passed += 1
            else:
                print(f"✗ FAIL: {description} - Expected {expected}, got {result}")
                failed += 1
        except Exception as e:
            print(f"✗ ERROR: {description} - Exception: {e}")
            failed += 1
    
    print(f"\nPing Detection Test Results: {passed} passed, {failed} failed")
    return failed == 0

def test_ping_handling():
    """Test ping packet handling functionality."""
    print("\nTesting ping packet handling...")
    
    test_cases = [
        ("123456789012345", "*************:5000"),
        ("987654321098765", "********:2020"),
        ("default_IMEI", "127.0.0.1:8080"),
    ]
    
    passed = 0
    failed = 0
    
    for imei, addr in test_cases:
        try:
            result = handle_ping_packet(imei, addr)
            if result:
                print(f"✓ PASS: Ping handled for IMEI {imei} from {addr}")
                passed += 1
            else:
                print(f"✗ FAIL: Ping handling failed for IMEI {imei} from {addr}")
                failed += 1
        except Exception as e:
            print(f"✗ ERROR: Ping handling for IMEI {imei} - Exception: {e}")
            failed += 1
    
    print(f"\nPing Handling Test Results: {passed} passed, {failed} failed")
    return failed == 0

def test_edge_cases():
    """Test edge cases and error conditions."""
    print("\nTesting edge cases...")
    
    edge_cases = [
        (None, False, "None data"),
        (123, False, "Integer data"),
        ("string", False, "String data"),
        ([], False, "List data"),
    ]
    
    passed = 0
    failed = 0
    
    for data, expected, description in edge_cases:
        try:
            result = is_ping_packet(data)
            if result == expected:
                print(f"✓ PASS: {description}")
                passed += 1
            else:
                print(f"✗ FAIL: {description} - Expected {expected}, got {result}")
                failed += 1
        except Exception as e:
            # For edge cases, we expect the function to handle errors gracefully
            if not expected:  # We expect False for these cases
                print(f"✓ PASS: {description} - Handled exception gracefully: {e}")
                passed += 1
            else:
                print(f"✗ FAIL: {description} - Unexpected exception: {e}")
                failed += 1
    
    print(f"\nEdge Cases Test Results: {passed} passed, {failed} failed")
    return failed == 0

def main():
    """Run all tests."""
    print("=" * 60)
    print("TELTONIKA PING IMPLEMENTATION TEST SUITE")
    print("=" * 60)
    
    all_passed = True
    
    # Run all test suites
    all_passed &= test_ping_detection()
    all_passed &= test_ping_handling()
    all_passed &= test_edge_cases()
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Implementation is ready for production.")
    else:
        print("❌ SOME TESTS FAILED! Please review the implementation.")
    print("=" * 60)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
